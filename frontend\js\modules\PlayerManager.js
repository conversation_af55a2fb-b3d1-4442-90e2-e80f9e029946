/**
 * PlayerManager.js - DEPRECATED
 *
 * ⚠️ DEPRECATED: Player management is now handled by UnifiedProfileManager.js
 *
 * Please use UnifiedProfileManager for new development.
 * This file is kept for backward compatibility only.
 *
 * Migration path:
 * - Old: playerManager.addPlayer(name)
 * - New: unifiedProfileManager.addPlayer(name, gameType)
 */

export class PlayerManager {
  constructor() {
    this.players = [];
    this.currentPlayerStats = null;
  }

  /**
   * Initialize player management functionality
   */
  async initialize() {
    try {
      console.log('🎯 Initializing player manager...');
      
      // Load player data first, then stats (since stats depend on having players loaded)
      await this.loadPlayerNames();
      await this.loadPlayerStats();
      
      // Setup UI handlers
      this.setupAddPlayerButton();
      this.setupAddPlayerForm();
      this.setupGameToggles();
      
      console.log('✅ Player manager initialized');
    } catch (error) {
      console.error('❌ Player manager initialization failed:', error);
      throw error;
    }
  }

  /**
   * Load player names associated with the user
   */
  async loadPlayerNames() {
    try {
      const response = await fetch('/api/ladder/my-players', { credentials: 'include' });
      
      if (!response.ok) {
        console.warn('No player names found');
        return;
      }

      this.players = await response.json();
      console.log('🔍 Raw player data from API:', this.players);
      
      this.displayPlayerCards();
      
      // Process stats immediately after loading players
      if (this.players && this.players.length > 0) {
        this.processPlayerStats();
      }
      
      console.log('✅ Player names loaded:', this.players.length);
    } catch (error) {
      console.error('Error loading player names:', error);
    }
  }

  /**
   * Load player statistics
   */
  async loadPlayerStats() {
    try {
      // Player stats are already included in the players data from loadPlayerNames
      // We don't need a separate API call for this
      if (this.players && this.players.length > 0) {
        this.processPlayerStats();
        console.log('✅ Player stats processed');
      } else {
        console.warn('No player stats found');
      }
    } catch (error) {
      console.error('Error processing player stats:', error);
    }
  }

  /**
   * Process player statistics from loaded players
   */
  processPlayerStats() {
    console.log('🔍 Processing stats for players:', this.players);
    
    // Calculate overall stats from all players
    let totalWins = 0;
    let totalLosses = 0;
    let totalMatches = 0;
    let highestMMR = 0;
    let highestRankPlayer = null;

    this.players.forEach(player => {
      console.log('🎮 Processing player:', player.name, 'Stats:', player.stats);
      
      if (player.stats) {
        totalWins += player.stats.wins || 0;
        totalLosses += player.stats.losses || 0;
        totalMatches += player.stats.totalMatches || (player.stats.wins || 0) + (player.stats.losses || 0);
      }

      const playerMMR = player.mmr || 0;
      if (playerMMR > highestMMR) {
        highestMMR = playerMMR;
        highestRankPlayer = player;
      }
    });

    console.log('📊 Calculated overall stats:', {
      totalWins, totalLosses, totalMatches, highestMMR, highestRankPlayer: highestRankPlayer?.name
    });

    // Update UI with aggregate stats
    this.updatePlayerStatsDisplay({
      totalWins,
      totalLosses,
      totalMatches,
      winRate: totalMatches > 0 ? Math.round((totalWins / totalMatches) * 100) : 0,
      highestRankPlayer
    });
  }

  /**
   * Update player stats display in UI
   */
  updatePlayerStatsDisplay(stats) {
    // Update overall stats
    this.updateElement('wins-losses', `${stats.totalWins}/${stats.totalLosses}`);
    this.updateElement('win-rate', `${stats.winRate}%`);

    // Update highest rank info
    if (stats.highestRankPlayer) {
      this.updateElement('highest-rank-name', stats.highestRankPlayer.rank?.name || 'Unranked');
      this.updateElement('highest-mmr', stats.highestRankPlayer.mmr || 0);
    }
  }

  /**
   * Helper method to safely update elements
   */
  updateElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = value;
    }
  }

  /**
   * Display player cards in the UI
   */
  displayPlayerCards() {
    const playersContainer = document.getElementById('player-names-container');
    if (!playersContainer) return;

    if (this.players.length === 0) {
      playersContainer.innerHTML = `
        <div class="no-players-message">
          <i class="fas fa-user-plus"></i>
          <h3>No Players Added Yet</h3>
          <p>Add your WC player names to track your statistics and match history.</p>
        </div>`;
      return;
    }

    playersContainer.innerHTML = this.players.map((player, index) => 
      this.createPlayerCard(player, index === 0)
    ).join('');
    
    // Attach event listeners after creating the HTML
    this.attachPlayerCardEvents();
  }

  /**
   * Attach event listeners to player card buttons
   */
  attachPlayerCardEvents() {
    // Attach click events to the entire clickable card area
    document.querySelectorAll('.player-card-clickable').forEach(cardArea => {
      cardArea.addEventListener('click', (e) => {
        e.stopPropagation();
        const playerId = cardArea.getAttribute('data-player-id');
        const player = this.players.find(p => (p._id || p.id) === playerId);
        if (player) {
          console.log('🎯 Player card clicked:', player.name);
          this.showPlayerStatsModal(player);
        }
      });
      
      // Add hover effect styling
      cardArea.style.cursor = 'pointer';
    });

    // Attach unlink button events
    document.querySelectorAll('.btn-unlink').forEach(button => {
      button.addEventListener('click', async (e) => {
        e.stopPropagation(); // Prevent triggering the card click
        const playerId = button.getAttribute('data-player-id');
        const player = this.players.find(p => (p._id || p.id) === playerId);
        if (player && confirm(`Are you sure you want to unlink player "${player.name}"? This action cannot be undone.`)) {
          await this.removePlayer(playerId);
        }
      });
    });
  }

  /**
   * Create HTML for a player card
   */
  createPlayerCard(player, isActive = false) {
    console.log('🎮 Creating card for player:', player);
    
    // Handle both _id and id fields
    const playerId = player._id || player.id;
    const playerName = player.name || 'Unknown Player';
    const playerMMR = player.mmr || 0;
    
    // Get rank info with fallback
    const rankInfo = this.getRankInfo(player);
    
    // Get stats with fallback  
    const wins = player.stats?.wins || 0;
    const losses = player.stats?.losses || 0;
    const totalMatches = player.stats?.totalMatches || (wins + losses);
    const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0;
    
    return `
      <div class="player-card ${isActive ? 'active' : ''}" data-player-id="${playerId}">
        <div class="player-card-clickable" data-player-id="${playerId}">
          <div class="player-basic-info">
            <img src="${rankInfo.image}" alt="${rankInfo.name}" class="player-rank-image">
            <div class="player-name-rank">
              <span class="player-name">${playerName}</span>
              <span class="player-rank">${rankInfo.name}</span>
            </div>
          </div>
          
          <div class="player-mmr-section">
            <span class="mmr-value">${playerMMR}</span>
            <span class="mmr-label">MMR</span>
          </div>
          
          <div class="player-stats-row">
            <div class="stat-group">
              <span class="stat-value">${totalMatches}</span>
              <span class="stat-label">Games</span>
            </div>
            <div class="stat-group">
              <span class="stat-value">${wins}/${losses}</span>
              <span class="stat-label">W/L</span>
            </div>
            <div class="stat-group">
              <span class="stat-value">${winRate}%</span>
              <span class="stat-label">Win Rate</span>
            </div>
          </div>
          
          <div class="player-action-hint">
            <i class="fas fa-chart-line"></i>
            <span>View Stats</span>
          </div>
        </div>
        
        <div class="player-unlink-action">
          <button class="btn-unlink" data-player-id="${playerId}" title="Unlink player">
            <i class="fas fa-unlink"></i>
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Get rank information for a player
   */
  getRankInfo(player) {
    const defaultRank = {
      rank: 'Unranked',
      name: 'Unranked',
      image: '/assets/img/ranks/emblem.png'
    };

    // Check multiple possible rank sources
    let rank = player.rank || player.currentRank;
    
    if (!rank) {
      // Try to determine rank from MMR if no rank is set
      const mmr = player.mmr || 0;
      if (mmr >= 2000) {
        rank = { name: 'Diamond', image: '/assets/img/ranks/a3.png' };
      } else if (mmr >= 1500) {
        rank = { name: 'Gold', image: '/assets/img/ranks/g1.png' };
      } else if (mmr >= 1000) {
        rank = { name: 'Silver', image: '/assets/img/ranks/s1.png' };
      } else if (mmr >= 500) {
        rank = { name: 'Bronze', image: '/assets/img/ranks/b1.png' };
      } else {
        rank = defaultRank;
      }
    }

    return {
      rank: rank.name || rank.title || 'Unranked',
      name: rank.name || rank.title || 'Unranked',
      image: rank.image || rank.icon || defaultRank.image
    };
  }

  /**
   * Format game name for display
   */
  formatGameName(game) {
    const gameNames = {
      'warcraft1': 'WC: Orcs & Humans',
      'warcraft2': 'WC II: Tides of Darkness',
      'warcraft3': 'WC III: Reign of Chaos'
    };
    return gameNames[game] || game;
  }

  /**
   * Setup add player button functionality
   */
  setupAddPlayerButton() {
    const addPlayerBtn = document.getElementById('add-player-btn');
    const addPlayerModal = document.getElementById('add-player-modal');
    const closeModalBtn = document.getElementById('close-add-player-modal');
    const cancelAddPlayerBtn = document.getElementById('cancel-add-player');

    console.log('🔧 Setting up add player button:', {
      button: !!addPlayerBtn,
      modal: !!addPlayerModal,
      closeBtn: !!closeModalBtn,
      cancelBtn: !!cancelAddPlayerBtn
    });

    if (!addPlayerBtn || !addPlayerModal) {
      console.warn('Add player elements not found');
      return;
    }

    // Show modal when add player button is clicked
    addPlayerBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔍 Add player button clicked - showing modal');
      addPlayerModal.style.display = 'flex';
      addPlayerModal.classList.add('show');
    });

    const closeModal = () => {
      console.log('❌ Closing add player modal');
      addPlayerModal.style.display = 'none';
      addPlayerModal.classList.remove('show');
      
      // Reset the form
      const formElement = document.getElementById('add-player-form');
      if (formElement) {
        formElement.reset();
      }
    };

    // Close modal when X button is clicked
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeModal();
      });
    }

    // Close modal when cancel button is clicked
    if (cancelAddPlayerBtn) {
      cancelAddPlayerBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeModal();
      });
    }

    // Close modal when clicking outside of it, but prevent clicks inside modal content from closing
    addPlayerModal.addEventListener('click', (e) => {
      if (e.target === addPlayerModal) {
        closeModal();
      }
    });

    // Prevent modal content clicks from bubbling up
    const modalContent = addPlayerModal.querySelector('.modal-content');
    if (modalContent) {
      modalContent.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }
  }

  /**
   * Setup add player form
   */
  setupAddPlayerForm() {
    const form = document.getElementById('add-player-form');
    const cancelBtn = document.getElementById('cancel-add-player');
    const modal = document.getElementById('add-player-modal');

    if (!form) return;

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(form);
      const playerData = {
        name: formData.get('playerName').trim(),
        game: formData.get('game') || 'warcraft3'
      };

      await this.addPlayer(playerData);
    });

    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        form.reset();
        modal.style.display = 'none';
      });
    }
  }

  /**
   * Add a new player
   */
  async addPlayer(playerData) {
    try {
      const response = await fetch('/api/ladder/add-player', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ username: playerData.name })
      });

      if (response.ok) {
        const result = await response.json();
        // Reload players to get updated list
        await this.loadPlayerNames();
        
        // Refresh avatar across the application
        if (window.AvatarUtils) {
          try {
            await window.AvatarUtils.refreshUserAvatar();
            console.log('✅ Avatar refreshed after player link');
          } catch (error) {
            console.warn('⚠️ Avatar refresh failed after player link:', error);
          }
        }
        
        // Close modal and reset form
        const modal = document.getElementById('add-player-modal');
        const form = document.getElementById('add-player-form');
        
        if (modal) modal.style.display = 'none';
        if (form) form.reset();
        
        this.showNotification('Player added successfully!', 'success');
      } else {
        const error = await response.json();
        this.showNotification(error.error || 'Failed to add player', 'error');
      }
    } catch (error) {
      console.error('Error adding player:', error);
      this.showNotification('Error adding player', 'error');
    }
  }

  /**
   * Remove a player from the user's account
   */
  async removePlayer(playerId) {
    try {
      console.log('🗑️ Removing player:', playerId);
      
      const response = await fetch('/api/ladder/unlink-player', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ playerId }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to unlink player');
      }

      console.log('✅ Player removed successfully');
      
      // Remove from local players array
      this.players = this.players.filter(p => (p._id || p.id) !== playerId);
      
      // Refresh the UI
      this.displayPlayerCards();
      this.processPlayerStats();
      
      // Refresh avatar across the application
      if (window.AvatarUtils) {
        try {
          await window.AvatarUtils.refreshUserAvatar();
          console.log('✅ Avatar refreshed after player unlink');
        } catch (error) {
          console.warn('⚠️ Avatar refresh failed after player unlink:', error);
        }
      }
      
      this.showNotification('Player unlinked successfully!', 'success');
      
    } catch (error) {
      console.error('❌ Error removing player:', error);
      this.showNotification('Failed to unlink player. Please try again.', 'error');
    }
  }

  /**
   * Show player details
   */
  viewPlayerDetails(playerId) {
    const player = this.players.find(p => p.id === playerId);
    if (!player) return;

    // TODO: Implement player details modal or navigate to player page
    console.log('Viewing player details for:', player);
    this.showNotification('Player details feature coming soon!', 'info');
  }

  /**
   * Show player stats modal
   */
  showPlayerStatsModal(player) {
    console.log('🔍 Showing stats modal for player:', player);
    
    // Set current player for subsequent operations
    this.currentPlayer = player;
    
    const modal = document.getElementById('player-stats-modal');
    const modalBody = modal?.querySelector('.modal-body');
    
    if (!modal || !modalBody) {
      console.error('Player stats modal not found');
      return;
    }

    // Show modal first
    modal.style.display = 'flex';
    modal.classList.add('show');

    // Setup modal tabs and content
    this.setupPlayerStatsModal();

    // Load default tab (overview)
    this.updateOverviewTab(player);

    console.log('✅ Player stats modal shown');
  }

  /**
   * Setup player stats modal functionality
   */
  setupPlayerStatsModal() {
    const modal = document.getElementById('player-stats-modal');
    const closeBtn = document.getElementById('close-player-stats-modal');
    
    if (!modal) return;

    const closeModal = () => {
      modal.style.display = 'none';
      modal.classList.remove('show');
    };

    // Close button
    if (closeBtn) {
      closeBtn.addEventListener('click', closeModal);
    }

    // Click outside to close
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Prevent modal content clicks from closing modal
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
      modalContent.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Setup tab switching
    const tabs = modal.querySelectorAll('.activity-tab');
    const tabContents = modal.querySelectorAll('.activity-content');

    console.log('🎯 Setting up stats modal tabs:', {
      tabsFound: tabs.length,
      contentsFound: tabContents.length
    });

    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const tabName = tab.getAttribute('data-tab');
        
        console.log(`📊 Switching to stats tab: ${tabName}`);
        
        // Update tab states
        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        
        // Update content states
        tabContents.forEach(content => {
          if (content.id === `${tabName}-tab`) {
            content.style.display = 'block';
          } else {
            content.style.display = 'none';
          }
        });
        
        // Load tab-specific data based on tab
        this.loadStatsTabData(tabName);
      });
    });

    // FIXED: Ensure Overview tab is active by default
    const overviewTab = modal.querySelector('.activity-tab[data-tab="overview"]');
    const overviewContent = document.getElementById('overview-tab');
    
    if (overviewTab && overviewContent) {
      // Reset all tabs
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(content => content.style.display = 'none');
      
      // Set overview as active
      overviewTab.classList.add('active');
      overviewContent.style.display = 'block';
      
      console.log('✅ Overview tab set as default in stats modal');
    }
  }

  /**
   * Load data for specific stats tab
   */
  loadStatsTabData(tabName) {
    console.log(`📊 Loading stats tab data for: ${tabName}`);
    
    switch(tabName) {
      case 'overview':
        // Overview is loaded when modal opens
        break;
      case 'matches':
        this.loadMatchHistory();
        break;
      case 'performance':
        this.loadPerformanceStats();
        break;
    }
  }

  /**
   * Load performance stats for the current player
   */
  async loadPerformanceStats() {
    if (!this.currentPlayer) {
      console.error('❌ No current player set for performance stats');
      return;
    }

    console.log('🔍 Loading performance stats for current player:', this.currentPlayer);

    // Check for the actual performance tab elements that exist in the HTML
    const mapsPerformanceGrid = document.getElementById('maps-performance-grid');
    const racesPerformanceGrid = document.getElementById('races-performance-grid');
    
    if (!mapsPerformanceGrid || !racesPerformanceGrid) {
      console.error('❌ Performance grids not found', {
        mapsGrid: !!mapsPerformanceGrid,
        racesGrid: !!racesPerformanceGrid
      });
      return;
    }

    // Show loading state in both grids
    mapsPerformanceGrid.innerHTML = `
      <div class="loading">Loading map statistics...</div>
    `;
    racesPerformanceGrid.innerHTML = `
      <div class="loading">Loading race statistics...</div>
    `;

    try {
      // Fetch player stats from the working API endpoint
      console.log('🎯 Fetching player stats from API...');
      const response = await fetch(`/api/ladder/player/${this.currentPlayer._id}/stats`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Player stats loaded:', data);

      // Render performance stats using the API data
      this.renderPerformanceStats(data.stats, mapsPerformanceGrid, racesPerformanceGrid);

    } catch (error) {
      console.error('❌ Error loading performance stats:', error);
      mapsPerformanceGrid.innerHTML = `
        <div class="stats-error">
          <i class="fas fa-exclamation-triangle"></i>
          <p>Error loading map data: ${error.message}</p>
        </div>
      `;
      racesPerformanceGrid.innerHTML = `
        <div class="stats-error">
          <i class="fas fa-exclamation-triangle"></i>
          <p>Error loading race data: ${error.message}</p>
        </div>
      `;
    }
  }

  /**
   * Render performance stats UI with the loaded data
   */
  renderPerformanceStats(stats, mapsGrid, racesGrid) {
    if (!mapsGrid || !racesGrid) return;

    // Race Performance Section
    let raceHTML = '';
    if (stats.races && Object.keys(stats.races).length > 0) {
      const raceNames = { human: 'Human', orc: 'Orc', random: 'Random' };
      
      Object.entries(stats.races).forEach(([race, games]) => {
        if (games > 0) {
          const wins = stats.raceWins?.[race] || 0;
          const winRate = (wins / games) * 100;
          const winRateClass = winRate >= 60 ? 'good' : winRate >= 40 ? 'average' : 'poor';
          
          raceHTML += `
            <div class="race-stat-card">
              <div class="race-header">
                <span class="race-icon">${this.getRaceIcon(race)}</span>
                <span class="race-name">${raceNames[race] || race}</span>
              </div>
              <div class="race-stats">
                <div class="stat-item">
                  <span class="stat-label">Games:</span>
                  <span class="stat-value">${games}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Wins:</span>
                  <span class="stat-value">${wins}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Win Rate:</span>
                  <span class="stat-value ${winRateClass}">${winRate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          `;
        }
      });
    } else {
      raceHTML = '<div class="no-data">No race data available</div>';
    }

    // Map Performance Section
    let mapHTML = '';
    if (stats.topMaps && stats.topMaps.length > 0) {
      stats.topMaps.slice(0, 6).forEach(map => {
        const winRateClass = map.winRate >= 60 ? 'good' : map.winRate >= 40 ? 'average' : 'poor';
        
        mapHTML += `
          <div class="map-stat-card">
            <div class="map-header">
              <span class="map-name">${map.name}</span>
            </div>
            <div class="map-stats">
              <div class="stat-item">
                <span class="stat-label">Games:</span>
                <span class="stat-value">${map.count}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Wins:</span>
                <span class="stat-value">${map.wins || 0}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Win Rate:</span>
                <span class="stat-value ${winRateClass}">${map.winRate.toFixed(1)}%</span>
              </div>
            </div>
          </div>
        `;
      });
    } else {
      mapHTML = '<div class="no-data">No map data available</div>';
    }

    // Update the actual DOM elements
    racesGrid.innerHTML = raceHTML;
    mapsGrid.innerHTML = mapHTML;
    
    console.log('✅ Performance stats UI rendered successfully');
  }

  /**
   * Get appropriate icon for race
   */
  getRaceIcon(race) {
    const raceIcons = {
      'human': 'fas fa-shield-alt',
      'orc': 'fas fa-fist-raised',
      'night elf': 'fas fa-leaf',
      'undead': 'fas fa-skull',
      'random': 'fas fa-dice'
    };
    
    return raceIcons[race.toLowerCase()] || 'fas fa-question';
  }

  /**
   * Setup game toggles
   */
  setupGameToggles() {
    const gameToggles = document.querySelectorAll('.game-toggle');
    
    gameToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        const game = toggle.dataset.game;
        this.filterPlayersByGame(game);
        
        // Update active state
        gameToggles.forEach(t => t.classList.remove('active'));
        toggle.classList.add('active');
      });
    });
  }

  /**
   * Filter players by game
   */
  filterPlayersByGame(game) {
    const filteredPlayers = game === 'all' ? 
      this.players : 
      this.players.filter(p => p.game === game);
    
    this.displayFilteredPlayers(filteredPlayers);
  }

  /**
   * Display filtered players
   */
  displayFilteredPlayers(players) {
    const playersContainer = document.getElementById('players-list');
    if (!playersContainer) return;

    if (players.length === 0) {
      playersContainer.innerHTML = `
        <div class="no-players-message">
          <i class="fas fa-filter"></i>
          <h3>No Players Found</h3>
          <p>No players found for the selected game filter.</p>
        </div>`;
      return;
    }

    playersContainer.innerHTML = players.map((player, index) => 
      this.createPlayerCard(player, index === 0)
    ).join('');
  }

  /**
   * Show notification (can be replaced with global notification system)
   */
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 80px;
      right: 20px;
      background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  /**
   * Load match history for the player stats modal
   */
  async loadMatchHistory() {
    console.log('📜 Loading match history for player stats modal...');
    
    const container = document.getElementById('matches-container');
    const paginationContainer = document.getElementById('matches-pagination');
    
    if (!container || !this.currentPlayer) return;

    try {
      // Show loading state
      container.innerHTML = '<div class="loading">Loading match history...</div>';
      
      // For demo purposes, we'll create some sample matches
      // In a real app, this would fetch from an API
      const allMatches = this.generateSampleMatches(this.currentPlayer);
      
      // Initialize pagination
      this.initializeMatchPagination(allMatches, container, paginationContainer);
      
    } catch (error) {
      console.error('Error loading match history:', error);
      container.innerHTML = '<div class="error-message">Failed to load match history</div>';
    }
  }

  /**
   * Generate sample matches for demonstration
   */
  generateSampleMatches(player) {
    const maps = ['Lost Temple', 'Green Isles', 'Plains of Snow', 'Nowhere to Run', 'High Seas Combat'];
    const races = ['Human', 'Orc', 'Undead', 'Night Elf'];
    const modes = ['1v1', '2v2', 'FFA'];
    const opponents = ['PlayerABC', 'WarriorX', 'StrategicMind', 'FastRush', 'TurtleDefense'];
    
    const matches = [];
    const matchCount = Math.min(player.stats.wins + player.stats.losses, 25); // Max 25 matches for demo
    
    for (let i = 0; i < matchCount; i++) {
      const isWin = Math.random() < (player.stats.wins / (player.stats.wins + player.stats.losses));
      const mmrChange = isWin ? Math.floor(Math.random() * 25) + 5 : -(Math.floor(Math.random() * 25) + 5);
      
      matches.push({
        id: `match_${i}`,
        result: isWin ? 'win' : 'loss',
        opponent: opponents[Math.floor(Math.random() * opponents.length)],
        map: maps[Math.floor(Math.random() * maps.length)],
        race: races[Math.floor(Math.random() * races.length)],
        mode: modes[Math.floor(Math.random() * modes.length)],
        duration: Math.floor(Math.random() * 30) + 5, // 5-35 minutes
        mmrChange: mmrChange,
        date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Last 30 days
      });
    }
    
    return matches.sort((a, b) => b.date - a.date); // Sort by newest first
  }

  /**
   * Initialize match history pagination
   */
  initializeMatchPagination(allMatches, container, paginationContainer) {
    const matchesPerPage = 5;
    let currentPage = 1;
    const totalPages = Math.ceil(allMatches.length / matchesPerPage);

    // Store pagination state
    this.matchPagination = {
      currentPage,
      totalPages,
      matchesPerPage,
      allMatches
    };

    const renderMatches = (page) => {
      const startIndex = (page - 1) * matchesPerPage;
      const endIndex = startIndex + matchesPerPage;
      const pageMatches = allMatches.slice(startIndex, endIndex);

      if (pageMatches.length === 0) {
        container.innerHTML = '<div class="empty-state">No matches found</div>';
        return;
      }

      // Create matches in table row format
      const matchesHtml = pageMatches.map(match => {
        const resultClass = match.result === 'win' ? 'win' : 'loss';
        const resultIcon = match.result === 'win' ? 'fas fa-trophy' : 'fas fa-times-circle';
        const mmrClass = match.mmrChange > 0 ? 'positive' : match.mmrChange < 0 ? 'negative' : 'neutral';
        const mmrPrefix = match.mmrChange > 0 ? '+' : '';
        
        return `
          <div class="match-row ${resultClass}">
            <div class="match-cell result ${resultClass}">
              <i class="${resultIcon}"></i>
              <span class="result-text">${match.result.toUpperCase()}</span>
            </div>
            <div class="match-cell opponent">
              <span class="opponent-name">${match.opponent}</span>
            </div>
            <div class="match-cell map">
              <i class="fas fa-map"></i>
              ${match.map}
            </div>
            <div class="match-cell race">
              <i class="fas fa-fist-raised"></i>
              ${match.race}
            </div>
            <div class="match-cell mode">
              <i class="fas fa-users"></i>
              ${match.mode}
            </div>
            <div class="match-cell duration">
              <i class="fas fa-clock"></i>
              ${match.duration}m
            </div>
            <div class="match-cell mmr">
              <span class="mmr-change ${mmrClass}">${mmrPrefix}${match.mmrChange}</span>
            </div>
            <div class="match-cell date">
              ${match.date.toLocaleDateString()}
            </div>
          </div>
        `;
      }).join('');

      container.innerHTML = `<div class="matches-rows">${matchesHtml}</div>`;

      // Update pagination controls
      if (paginationContainer && totalPages > 1) {
        paginationContainer.style.display = 'flex';
        
        const pageInfo = document.getElementById('matches-page-info');
        const prevBtn = document.getElementById('prev-matches');
        const nextBtn = document.getElementById('next-matches');
        
        if (pageInfo) {
          pageInfo.textContent = `Page ${page} of ${totalPages}`;
        }
        
        if (prevBtn) {
          prevBtn.disabled = page === 1;
        }
        
        if (nextBtn) {
          nextBtn.disabled = page === totalPages;
        }
      } else if (paginationContainer) {
        paginationContainer.style.display = 'none';
      }
    };

    // Initial render
    renderMatches(currentPage);

    // Setup pagination event listeners (only once)
    if (paginationContainer && !paginationContainer.hasAttribute('data-initialized')) {
      const prevBtn = document.getElementById('prev-matches');
      const nextBtn = document.getElementById('next-matches');

      if (prevBtn) {
        prevBtn.addEventListener('click', () => {
          if (this.matchPagination.currentPage > 1) {
            this.matchPagination.currentPage--;
            renderMatches(this.matchPagination.currentPage);
          }
        });
      }

      if (nextBtn) {
        nextBtn.addEventListener('click', () => {
          if (this.matchPagination.currentPage < this.matchPagination.totalPages) {
            this.matchPagination.currentPage++;
            renderMatches(this.matchPagination.currentPage);
          }
        });
      }

      paginationContainer.setAttribute('data-initialized', 'true');
    }
  }

  /**
   * Update overview tab with player data
   */
  updateOverviewTab(player) {
    console.log('📊 Updating overview tab for player:', player.name);
    
    // Update overview stats
    this.updateElement('overview-mmr', player.mmr || 0);
    this.updateElement('overview-rank', player.rank?.name || 'Unranked');
    this.updateElement('overview-winrate', `${((player.stats?.wins || 0) / ((player.stats?.wins || 0) + (player.stats?.losses || 0)) * 100 || 0).toFixed(1)}%`);
    this.updateElement('overview-games', (player.stats?.wins || 0) + (player.stats?.losses || 0));
    
    // Create overview chart
    this.createOverviewChart(player);
  }

  /**
   * Create overview chart for player stats modal
   */
  createOverviewChart(player) {
    const canvas = document.getElementById('overview-chart');
    if (!canvas) return;

    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
      console.warn('Chart.js not loaded, showing placeholder');
      const ctx = canvas.getContext('2d');
      canvas.width = 200;
      canvas.height = 200;

      // Simple placeholder chart
      ctx.fillStyle = '#3b82f6';
      ctx.fillRect(50, 150, 40, -(player.stats?.wins || 0) * 2);
      
      ctx.fillStyle = '#ef4444';
      ctx.fillRect(100, 150, 40, -(player.stats?.losses || 0) * 2);

      ctx.fillStyle = '#ffffff';
      ctx.font = '12px Arial';
      ctx.fillText('Wins', 60, 170);
      ctx.fillText('Losses', 105, 170);
      return;
    }

    // Destroy existing chart if it exists
    if (canvas.chart) {
      canvas.chart.destroy();
    }

    const ctx = canvas.getContext('2d');
    
    const wins = player.stats?.wins || 0;
    const losses = player.stats?.losses || 0;
    
    canvas.chart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Wins', 'Losses'],
        datasets: [{
          data: [wins, losses],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(239, 68, 68, 0.8)'
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(239, 68, 68, 1)'
          ],
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              color: '#ffffff',
              font: {
                size: 11
              }
            }
          }
        }
      }
    });
  }
}

// Global instance for backward compatibility
window.playerManager = new PlayerManager(); 