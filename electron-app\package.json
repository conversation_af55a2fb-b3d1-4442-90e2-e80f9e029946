{"name": "wc-arena-core", "version": "1.0.0", "description": "WC Arena Core - Advanced Desktop Application for Warcraft Arena", "main": "main.js", "scripts": {"start": "electron .", "start-dev": "electron . --dev", "build": "electron-builder --publish=never", "build:publish": "electron-builder", "pack": "electron-builder --dir", "clean": "rm -rf dist", "postinstall": "electron-builder install-app-deps", "lint": "echo 'ESLint not configured'", "test": "echo 'Tests not configured'"}, "dependencies": {"auto-launch": "^5.0.5", "axios": "^1.10.0", "canvas": "^3.1.0", "chokidar": "^3.5.3", "electron": "^32.0.1", "electron-store": "^10.0.0", "express": "^4.19.2", "http-proxy-middleware": "^3.0.0", "jimp": "^0.22.10", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "sharp": "^0.33.4", "sql.js": "^1.13.0", "tesseract.js": "^5.1.1", "ws": "^8.18.0"}, "devDependencies": {"electron-builder": "^25.0.5", "electron-rebuild": "^3.6.0"}, "build": {"appId": "com.warcraftarena.core", "productName": "WC Arena Core", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraFiles": [{"from": "assets/", "to": "assets/"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.png", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.png", "category": "public.app-category.games"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Game"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "author": "WC Arena Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/warcraftarena/companion.git"}, "keywords": ["warcraft", "arena", "gaming", "core", "electron", "screenshot", "analysis", "ai", "ladder", "esports"]}