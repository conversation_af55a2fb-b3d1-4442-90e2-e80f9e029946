/**
 * MyProfile - Unified Profile System
 *
 * Now uses the UnifiedProfileManager for consistent User/Player profile handling
 */

import { unifiedProfileManager } from './modules/UnifiedProfileManager.js';
import { unifiedProfileUI } from './modules/UnifiedProfileUI.js';

// Initialize the unified profile system when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 Starting Unified Profile System (My Profile)...');

  try {
    // Initialize the unified profile manager for "my" profile
    await unifiedProfileManager.init('my');

    console.log('✅ My Profile system ready!');

  } catch (error) {
    console.error('❌ Failed to initialize my profile system:', error);

    // Show user-friendly error
    const errorContainer = document.getElementById('profile-error-container') || document.body;
    errorContainer.innerHTML = `
      <div class="profile-init-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Profile System Error</h3>
        <p>Failed to initialize the profile system. Please refresh the page to try again.</p>
        <button onclick="window.location.reload()" class="btn btn-primary">
          <i class="fas fa-refresh"></i> Refresh Page
        </button>
      </div>
    `;
  }
});

/**
 * Setup legacy compatibility functions for backward compatibility
 * This ensures any old HTML or remaining code can still function
 */
function setupLegacyCompatibility() {
  console.log('🔧 Setting up unified profile legacy compatibility...');

  // Global functions that may be called from HTML onclick handlers
  window.addPlayer = async (playerName, gameType = 'war2') => {
    try {
      await unifiedProfileManager.addPlayer(playerName, gameType);
      console.log(`✅ Added player ${playerName} for ${gameType}`);
    } catch (error) {
      console.error('❌ Failed to add player:', error);
      alert(`Failed to add player: ${error.message}`);
    }
  };

  window.removePlayer = async (gameType) => {
    try {
      await unifiedProfileManager.removePlayer(gameType);
      console.log(`✅ Removed player for ${gameType}`);
    } catch (error) {
      console.error('❌ Failed to remove player:', error);
      alert(`Failed to remove player: ${error.message}`);
    }
  };

  window.switchTab = (tabName) => {
    unifiedProfileUI.switchTab(tabName);
  };

  window.refreshProfile = async () => {
    try {
      await unifiedProfileManager.init('my');
      console.log('✅ Profile refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh profile:', error);
    }
  };
  
  // Expose clan manager directly for onclick handlers
  window.clanManager = profileManager.modules.clanManager;
  console.log('🏰 Clan manager exposed to window:', !!window.clanManager);
  
  // Clan management functions with error handling
  window.showCreateClanModal = () => {
    console.log('🏰 showCreateClanModal called');
    if (!profileManager.modules.clanManager) {
      console.error('❌ Clan manager not available');
      return;
    }
    return profileManager.modules.clanManager.showCreateClanModal();
  };
  window.showFindClansModal = () => {
    console.log('🏰 showFindClansModal called');
    if (!profileManager.modules.clanManager) {
      console.error('❌ Clan manager not available');
      return;
    }
    return profileManager.modules.clanManager.showBrowseClansModal();
  };
  window.viewClanDetails = (clanId) => {
    console.log('🏰 viewClanDetails called for clan:', clanId);
    if (!profileManager.modules.clanManager) {
      console.error('❌ Clan manager not available');
      return;
    }
    return profileManager.modules.clanManager.loadClanDetails(clanId);
  };
  window.manageClan = (clanId) => {
    console.log('🏰 manageClan called for clan:', clanId);
    if (!profileManager.modules.clanManager) {
      console.error('❌ Clan manager not available');
      return;
    }
    return profileManager.modules.clanManager.showClanManagement(clanId);
  };
  window.leaveClan = (clanId) => {
    console.log('🏰 leaveClan called for clan:', clanId);
    if (!profileManager.modules.clanManager) {
      console.error('❌ Clan manager not available');
      return;
    }
    return profileManager.modules.clanManager.showLeaveClanModal(clanId);
  };
  window.joinClan = (clanId) => {
    console.log('🏰 joinClan called for clan:', clanId);
    if (!profileManager.modules.clanManager) {
      console.error('❌ Clan manager not available');
      return;
    }
    return profileManager.modules.clanManager.showApplyToClanModal(clanId);
  };
  
  // Legacy data access (for any remaining old code)
  window.getCurrentUser = async () => {
    const userData = await profileManager.modules.dataLoader.loadUserProfile();
    return userData;
  };
  
  window.getPlayerData = async () => {
    const playerData = await profileManager.modules.dataLoader.loadPlayerStats();
    return playerData;
  };
  
  // Legacy UI update functions (for any remaining old code)
  window.updateUserProfile = (userData) => {
    return profileManager.modules.uiManager.updateUserProfile(userData);
  };
  
  window.updatePlayerStats = (playerStats) => {
    return profileManager.modules.uiManager.updatePlayerStats(playerStats);
  };
  
  // Development and debugging helpers
  window.getProfileSystemStatus = () => profileManager.getSystemStatus();
  window.emergencyResetProfile = () => profileManager.emergencyReset();
  
  // Setup delete account functionality after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupDeleteAccountHandlers);
  } else {
    setupDeleteAccountHandlers();
  }
  
  console.log('✅ Legacy compatibility setup complete');
}

/**
 * Setup delete account modal and functionality
 */
function setupDeleteAccountHandlers() {
  console.log('🚀 Setting up delete account handlers...');
  console.log('🔍 DOM ready state:', document.readyState);
  
  const deleteBtn = document.getElementById('delete-account-btn');
  const modal = document.getElementById('delete-account-modal');
  const closeBtn = document.getElementById('close-delete-account-modal');
  const cancelBtn = document.getElementById('cancel-delete-account');
  const confirmBtn = document.getElementById('confirm-delete-account');
  const confirmationInput = document.getElementById('delete-confirmation');
  
  console.log('🔍 Element check:', {
    deleteBtn: !!deleteBtn,
    modal: !!modal,
    closeBtn: !!closeBtn,
    cancelBtn: !!cancelBtn,
    confirmBtn: !!confirmBtn,
    confirmationInput: !!confirmationInput
  });
  
  if (!deleteBtn || !modal) {
    console.error('❌ Delete account elements not found, skipping setup');
    console.log('Missing elements:', { deleteBtn, modal });
    return;
  }
  
  // Show modal when delete button is clicked
  deleteBtn.addEventListener('click', (e) => {
    console.log('🔥 Delete button clicked!', e);
    
    // Debug modal state before showing
    console.log('🔍 Modal state before:', {
      display: modal.style.display,
      visibility: window.getComputedStyle(modal).visibility,
      opacity: window.getComputedStyle(modal).opacity,
      zIndex: window.getComputedStyle(modal).zIndex,
      position: window.getComputedStyle(modal).position,
      top: window.getComputedStyle(modal).top,
      left: window.getComputedStyle(modal).left
    });
    
    // Show modal using CSS classes
    modal.style.display = 'block';
    modal.classList.add('show');
    
    // Debug modal state after showing
    console.log('🔍 Modal state after:', {
      display: modal.style.display,
      visibility: window.getComputedStyle(modal).visibility,
      opacity: window.getComputedStyle(modal).opacity,
      zIndex: window.getComputedStyle(modal).zIndex,
      position: window.getComputedStyle(modal).position,
      boundingRect: modal.getBoundingClientRect()
    });
    
    confirmationInput.value = '';
    confirmBtn.disabled = true;
    confirmBtn.style.opacity = '0.5';
    confirmBtn.style.cursor = 'not-allowed';
  });
  
  console.log('✅ Event listener attached to delete button');
  
  // Hide modal functions
  const hideModal = () => {
    modal.style.display = 'none';
    modal.classList.remove('show');
  };
  
  closeBtn?.addEventListener('click', hideModal);
  cancelBtn?.addEventListener('click', hideModal);
  
  // Close modal when clicking outside
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      hideModal();
    }
  });
  
  // Enable/disable confirm button based on input
  confirmationInput?.addEventListener('input', (e) => {
    const value = e.target.value.trim();
    const isValid = value === 'DELETE';
    
    confirmBtn.disabled = !isValid;
    if (isValid) {
      confirmBtn.style.opacity = '1';
      confirmBtn.style.cursor = 'pointer';
    } else {
      confirmBtn.style.opacity = '0.5';
      confirmBtn.style.cursor = 'not-allowed';
    }
  });
  
  // Handle account deletion
  confirmBtn?.addEventListener('click', async () => {
    if (confirmBtn.disabled) return;
    
    try {
      // Show loading state
      confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
      confirmBtn.disabled = true;
      
      // Make API call to delete account
      const response = await fetch('/api/users/delete-account', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });
      
      const result = await response.json();
      
      if (response.ok) {
        // Show success message and redirect
        modal.innerHTML = `
          <div class="modal-content" style="max-width: 400px; text-align: center;">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
              <h2><i class="fas fa-check-circle"></i> Account Deleted</h2>
            </div>
            <div class="modal-body">
              <p style="color: #e2e8f0; margin-bottom: 1.5rem;">
                Your account has been successfully deleted. You will be redirected to the home page in 3 seconds.
              </p>
              <div style="color: #20c997;">
                <i class="fas fa-spinner fa-spin"></i> Redirecting...
              </div>
            </div>
          </div>
        `;
        
        // Redirect to home page after 3 seconds
        setTimeout(() => {
          window.location.href = '/';
        }, 3000);
        
      } else {
        throw new Error(result.error || 'Failed to delete account');
      }
      
    } catch (error) {
      console.error('Delete account error:', error);
      
      // Show error message
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = 'background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 8px; padding: 1rem; margin-top: 1rem; color: #dc3545;';
      errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        Error: ${error.message}
      `;
      
      modal.querySelector('.modal-body').appendChild(errorDiv);
      
      // Reset button state
      confirmBtn.innerHTML = '<i class="fas fa-trash-alt"></i> Delete My Account Forever';
      confirmBtn.disabled = false;
    }
  });
  
  console.log('✅ Delete account handlers setup complete');
}

/**
 * Handle any uncaught errors in the profile system
 */
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('profile')) {
    console.error('🚨 Profile System Error:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
    
    // Could send error report to server here
    // sendErrorReport(event);
  }
});

/**
 * Handle unhandled promise rejections in profile system
 */
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && event.reason.message.includes('profile')) {
    console.error('🚨 Profile System Promise Rejection:', event.reason);
    event.preventDefault(); // Prevent default browser error handling
  }
});

console.log('📄 MyProfile Refactored loaded successfully'); 