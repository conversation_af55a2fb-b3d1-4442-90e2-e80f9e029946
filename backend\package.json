{"name": "newsite-backend", "version": "1.0.0", "description": "Backend for newsite application", "main": "index.js", "scripts": {"start": "node index.js", "start:https": "node start-https.js", "dev": "nodemon index.js", "dev:https": "nodemon start-https.js", "db:admin": "node start-mongo-express.js", "db:seed": "node scripts/seed-database.js", "db:reset": "node scripts/seed-database.js", "tools:css-analyze": "node scripts/analyze-css.js", "tools:maps-cleanup": "node scripts/cleanupMapsData.js", "tools:maps-import": "node scripts/enhancedImportMaps.js", "tools:maps-reimport": "npm run tools:maps-cleanup && npm run tools:maps-import", "tools:forum-categories": "node update-forum-categories.js", "tools:fix-avatars": "node scripts/fix-avatar-paths.js", "lint": "echo 'ESLint not configured'", "test": "echo 'Tests not configured'"}, "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "axios": "^1.10.0", "bcrypt": "^5.1.1", "canvas": "^3.1.0", "chokidar": "^3.5.3", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-session": "^1.18.1", "geoip-lite": "^1.4.10", "googleapis": "^149.0.0", "jimp": "^0.22.10", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "mongoose": "^8.5.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-discord": "^0.1.4", "passport-google-oauth20": "^2.0.0", "passport-twitch-new": "^0.0.3", "sharp": "^0.33.4", "socket.io": "^4.8.1", "squareup": "^1.0.0", "tesseract.js": "^5.1.1", "xml2js": "^0.6.2"}, "devDependencies": {"nodemon": "^3.1.10"}}