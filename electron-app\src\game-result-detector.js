/**
 * Game Result Detector
 * 
 * Automatically detects game results from Warcraft 1/2/3 clients
 * Uses multiple detection methods for maximum reliability
 */

const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const { EventEmitter } = require('events');

class GameResultDetector extends EventEmitter {
  constructor(gameDetector, authManager) {
    super();
    this.gameDetector = gameDetector;
    this.authManager = authManager;
    
    // Detection methods
    this.logFileMonitor = new LogFileMonitor(this);
    this.replayAnalyzer = new ReplayAnalyzer(this);
    this.memoryReader = new MemoryReader(this);
    
    // Game-specific configurations
    this.gameConfigs = {
      'warcraft1': {
        name: 'Warcraft: Orcs & Humans',
        logPaths: [
          '%USERPROFILE%\\Documents\\Warcraft\\Logs',
          '%PROGRAMFILES%\\Warcraft\\Logs',
          '%PROGRAMFILES(X86)%\\Warcraft\\Logs'
        ],
        replayPaths: [
          '%USERPROFILE%\\Documents\\Warcraft\\Replays'
        ],
        processNames: ['Warcraft.exe', 'War1.exe'],
        resultPatterns: [
          /Victory!/i,
          /Defeat!/i,
          /Game Over/i
        ]
      },
      'warcraft2': {
        name: 'Warcraft II: Tides of Darkness',
        logPaths: [
          '%USERPROFILE%\\Documents\\Warcraft II BNE\\Logs',
          '%PROGRAMFILES%\\Warcraft II BNE\\Logs',
          '%PROGRAMFILES(X86)%\\Warcraft II BNE\\Logs'
        ],
        replayPaths: [
          '%USERPROFILE%\\Documents\\Warcraft II BNE\\Replays'
        ],
        processNames: ['Warcraft II BNE.exe', 'War2.exe'],
        resultPatterns: [
          /You have won!/i,
          /You have been defeated!/i,
          /Victory/i,
          /Defeat/i
        ]
      },
      'warcraft3': {
        name: 'Warcraft III',
        logPaths: [
          '%USERPROFILE%\\Documents\\Warcraft III\\Logs',
          '%PROGRAMFILES%\\Warcraft III\\Logs',
          '%PROGRAMFILES(X86)%\\Warcraft III\\Logs'
        ],
        replayPaths: [
          '%USERPROFILE%\\Documents\\Warcraft III\\Replays'
        ],
        processNames: ['Warcraft III.exe', 'war3.exe', 'Frozen Throne.exe'],
        resultPatterns: [
          /Victory!/i,
          /Defeat!/i,
          /has won the game!/i,
          /has left the game/i
        ]
      }
    };
    
    this.activeGames = new Map();
    this.isMonitoring = false;
  }

  /**
   * Start monitoring for game results
   */
  async startMonitoring() {
    if (this.isMonitoring) return;
    
    console.log('🎮 Starting game result detection...');
    this.isMonitoring = true;
    
    // Start all detection methods
    await this.logFileMonitor.start();
    await this.replayAnalyzer.start();
    await this.memoryReader.start();
    
    // Monitor for game launches
    this.gameDetector.on('gameStarted', (gameInfo) => {
      this.onGameStarted(gameInfo);
    });
    
    this.gameDetector.on('gameEnded', (gameInfo) => {
      this.onGameEnded(gameInfo);
    });
    
    console.log('✅ Game result detection started');
  }

  /**
   * Stop monitoring
   */
  async stopMonitoring() {
    if (!this.isMonitoring) return;
    
    console.log('🛑 Stopping game result detection...');
    this.isMonitoring = false;
    
    await this.logFileMonitor.stop();
    await this.replayAnalyzer.stop();
    await this.memoryReader.stop();
    
    this.activeGames.clear();
    console.log('✅ Game result detection stopped');
  }

  /**
   * Handle game started event
   */
  onGameStarted(gameInfo) {
    console.log(`🎮 Game started: ${gameInfo.name} (${gameInfo.type})`);
    
    const gameSession = {
      id: Date.now().toString(),
      type: gameInfo.type,
      name: gameInfo.name,
      processId: gameInfo.processId,
      startTime: Date.now(),
      players: [],
      result: null,
      detectionMethods: []
    };
    
    this.activeGames.set(gameSession.id, gameSession);
    
    // Configure detection for this specific game
    this.configureDetectionForGame(gameSession);
  }

  /**
   * Handle game ended event
   */
  onGameEnded(gameInfo) {
    console.log(`🎮 Game ended: ${gameInfo.name}`);
    
    // Find the corresponding game session
    for (const [sessionId, session] of this.activeGames.entries()) {
      if (session.processId === gameInfo.processId) {
        session.endTime = Date.now();
        
        // Try to detect final result if not already detected
        if (!session.result) {
          this.performFinalResultDetection(session);
        }
        
        // Emit result if we have one
        if (session.result) {
          this.emit('gameResult', session);
        }
        
        this.activeGames.delete(sessionId);
        break;
      }
    }
  }

  /**
   * Configure detection methods for a specific game
   */
  configureDetectionForGame(gameSession) {
    const config = this.gameConfigs[gameSession.type];
    if (!config) return;
    
    // Set up log file monitoring for this game
    this.logFileMonitor.addGameSession(gameSession, config);
    
    // Set up replay monitoring
    this.replayAnalyzer.addGameSession(gameSession, config);
    
    // Set up memory reading
    this.memoryReader.addGameSession(gameSession, config);
  }

  /**
   * Perform final result detection when game ends
   */
  async performFinalResultDetection(gameSession) {
    console.log(`🔍 Performing final result detection for ${gameSession.name}`);
    
    // Try each detection method
    const methods = [
      () => this.logFileMonitor.getFinalResult(gameSession),
      () => this.replayAnalyzer.getFinalResult(gameSession),
      () => this.memoryReader.getFinalResult(gameSession)
    ];
    
    for (const method of methods) {
      try {
        const result = await method();
        if (result) {
          gameSession.result = result;
          console.log(`✅ Final result detected: ${result.outcome}`);
          return;
        }
      } catch (error) {
        console.warn('Detection method failed:', error.message);
      }
    }
    
    console.log('⚠️ Could not detect final game result');
  }

  /**
   * Report detected result
   */
  reportResult(gameSession, result, detectionMethod) {
    console.log(`🎯 Game result detected via ${detectionMethod}:`, result);
    
    gameSession.result = result;
    gameSession.detectionMethods.push(detectionMethod);
    
    // Emit the result
    this.emit('gameResult', gameSession);
  }
}

/**
 * Log File Monitor
 * Monitors game log files for result indicators
 */
class LogFileMonitor {
  constructor(detector) {
    this.detector = detector;
    this.watchers = new Map();
    this.logBuffers = new Map();
  }

  async start() {
    console.log('📄 Starting log file monitoring...');
    // Implementation will be added
  }

  async stop() {
    for (const watcher of this.watchers.values()) {
      await watcher.close();
    }
    this.watchers.clear();
    this.logBuffers.clear();
  }

  addGameSession(gameSession, config) {
    // Set up file watchers for this game's log directories
    config.logPaths.forEach(logPath => {
      const expandedPath = this.expandPath(logPath);
      if (fs.existsSync(expandedPath)) {
        this.setupLogWatcher(gameSession, expandedPath, config);
      }
    });
  }

  setupLogWatcher(gameSession, logPath, config) {
    const watcher = chokidar.watch(logPath, {
      ignored: /[\/\\]\./,
      persistent: true,
      ignoreInitial: false
    });

    watcher.on('change', (filePath) => {
      this.analyzeLogFile(gameSession, filePath, config);
    });

    this.watchers.set(`${gameSession.id}-${logPath}`, watcher);
  }

  analyzeLogFile(gameSession, filePath, config) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      // Look for result patterns
      for (const line of lines.slice(-50)) { // Check last 50 lines
        for (const pattern of config.resultPatterns) {
          if (pattern.test(line)) {
            const result = this.parseResultFromLine(line, pattern);
            if (result) {
              this.detector.reportResult(gameSession, result, 'logFile');
              return;
            }
          }
        }
      }
    } catch (error) {
      console.warn('Error reading log file:', error.message);
    }
  }

  parseResultFromLine(line, pattern) {
    // Parse the result from the log line
    if (/victory|won/i.test(line)) {
      return { outcome: 'victory', timestamp: Date.now() };
    } else if (/defeat|lost/i.test(line)) {
      return { outcome: 'defeat', timestamp: Date.now() };
    }
    return null;
  }

  expandPath(pathTemplate) {
    return pathTemplate
      .replace(/%USERPROFILE%/g, process.env.USERPROFILE || '')
      .replace(/%PROGRAMFILES%/g, process.env.PROGRAMFILES || '')
      .replace(/%PROGRAMFILES\(X86\)%/g, process.env['PROGRAMFILES(X86)'] || '');
  }

  async getFinalResult(gameSession) {
    // Try to get final result from logs
    return null;
  }
}

/**
 * Replay Analyzer
 * Analyzes replay files for game results
 */
class ReplayAnalyzer {
  constructor(detector) {
    this.detector = detector;
    this.watchers = new Map();
  }

  async start() {
    console.log('🎬 Starting replay file monitoring...');
  }

  async stop() {
    for (const watcher of this.watchers.values()) {
      await watcher.close();
    }
    this.watchers.clear();
  }

  addGameSession(gameSession, config) {
    // Monitor replay directories
    config.replayPaths.forEach(replayPath => {
      const expandedPath = this.expandPath(replayPath);
      if (fs.existsSync(expandedPath)) {
        this.setupReplayWatcher(gameSession, expandedPath);
      }
    });
  }

  setupReplayWatcher(gameSession, replayPath) {
    const watcher = chokidar.watch(replayPath, {
      ignored: /[\/\\]\./,
      persistent: true
    });

    watcher.on('add', (filePath) => {
      // New replay file created
      setTimeout(() => {
        this.analyzeReplayFile(gameSession, filePath);
      }, 2000); // Wait for file to be fully written
    });

    this.watchers.set(`${gameSession.id}-${replayPath}`, watcher);
  }

  analyzeReplayFile(gameSession, filePath) {
    // Analyze replay file for results
    // This would require game-specific replay parsing
    console.log(`🎬 Analyzing replay: ${filePath}`);
  }

  expandPath(pathTemplate) {
    return pathTemplate
      .replace(/%USERPROFILE%/g, process.env.USERPROFILE || '')
      .replace(/%PROGRAMFILES%/g, process.env.PROGRAMFILES || '')
      .replace(/%PROGRAMFILES\(X86\)%/g, process.env['PROGRAMFILES(X86)'] || '');
  }

  async getFinalResult(gameSession) {
    return null;
  }
}

/**
 * Memory Reader
 * Reads game memory for result information (Advanced)
 */
class MemoryReader {
  constructor(detector) {
    this.detector = detector;
    this.activeReaders = new Map();
  }

  async start() {
    console.log('🧠 Starting memory reading...');
  }

  async stop() {
    this.activeReaders.clear();
  }

  addGameSession(gameSession, config) {
    // Set up memory reading for this game process
    // This would require native modules or external tools
    console.log(`🧠 Setting up memory reading for ${gameSession.name}`);
  }

  async getFinalResult(gameSession) {
    return null;
  }
}

/**
 * Game Process Detector
 * Detects when Warcraft games are launched and terminated
 */
class GameProcessDetector extends EventEmitter {
  constructor() {
    super();
    this.monitorInterval = null;
    this.knownProcesses = new Set();
    this.gameProcesses = new Map();
  }

  start() {
    if (this.monitorInterval) return;

    console.log('🔍 Starting game process detection...');

    this.monitorInterval = setInterval(() => {
      this.checkForGameProcesses();
    }, 2000); // Check every 2 seconds
  }

  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.knownProcesses.clear();
    this.gameProcesses.clear();
  }

  async checkForGameProcesses() {
    try {
      const { exec } = require('child_process');

      // Get all running processes
      exec('tasklist /fo csv', (error, stdout) => {
        if (error) return;

        const processes = this.parseTasklist(stdout);
        const currentGameProcesses = new Set();

        // Check for game processes
        for (const gameType in this.getGameConfigs()) {
          const config = this.getGameConfigs()[gameType];

          for (const processName of config.processNames) {
            const process = processes.find(p =>
              p.name.toLowerCase() === processName.toLowerCase()
            );

            if (process) {
              const processKey = `${gameType}-${process.pid}`;
              currentGameProcesses.add(processKey);

              // New game process detected
              if (!this.gameProcesses.has(processKey)) {
                const gameInfo = {
                  type: gameType,
                  name: config.name,
                  processId: process.pid,
                  processName: process.name,
                  startTime: Date.now()
                };

                this.gameProcesses.set(processKey, gameInfo);
                this.emit('gameStarted', gameInfo);
              }
            }
          }
        }

        // Check for ended games
        for (const [processKey, gameInfo] of this.gameProcesses.entries()) {
          if (!currentGameProcesses.has(processKey)) {
            this.gameProcesses.delete(processKey);
            this.emit('gameEnded', gameInfo);
          }
        }
      });
    } catch (error) {
      console.warn('Error checking game processes:', error.message);
    }
  }

  parseTasklist(output) {
    const lines = output.split('\n').slice(1); // Skip header
    return lines.map(line => {
      const parts = line.split('","').map(part => part.replace(/"/g, ''));
      return {
        name: parts[0],
        pid: parts[1],
        sessionName: parts[2],
        sessionNumber: parts[3],
        memUsage: parts[4]
      };
    }).filter(p => p.name && p.pid);
  }

  getGameConfigs() {
    return {
      'warcraft1': {
        name: 'Warcraft: Orcs & Humans',
        processNames: ['Warcraft.exe', 'War1.exe']
      },
      'warcraft2': {
        name: 'Warcraft II: Tides of Darkness',
        processNames: ['Warcraft II BNE.exe', 'War2.exe']
      },
      'warcraft3': {
        name: 'Warcraft III',
        processNames: ['Warcraft III.exe', 'war3.exe', 'Frozen Throne.exe']
      }
    };
  }
}

module.exports = { GameResultDetector, GameProcessDetector };
