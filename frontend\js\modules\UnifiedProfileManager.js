/**
 * Unified Profile Manager
 * 
 * Handles both User profiles and Player profiles with proper separation:
 * - User: Account holder with auth, bio, social links, achievements
 * - Player: Game-specific identity with M<PERSON>, stats, match history per game type
 * - Relationship: One User can have multiple Players across game types
 */

import { ApiClient } from './ApiClient.js';

export class UnifiedProfileManager {
  constructor() {
    this.apiClient = new ApiClient();
    this.cache = new Map();
    this.currentUser = null;
    this.currentPlayers = new Map(); // gameType -> Player data
    this.viewMode = 'user'; // 'user', 'player', 'combined'
    this.isOwnProfile = false;
  }

  /**
   * Initialize profile manager
   * @param {string} profileType - 'my', 'other', 'player'
   * @param {string} identifier - userId for user profiles, playerName for player profiles
   */
  async init(profileType = 'my', identifier = null) {
    console.log(`🚀 Initializing Unified Profile Manager - Type: ${profileType}, ID: ${identifier}`);
    
    try {
      this.profileType = profileType;
      this.identifier = identifier;
      this.isOwnProfile = profileType === 'my';

      // Load appropriate profile data based on type
      switch (profileType) {
        case 'my':
          await this.loadMyProfile();
          break;
        case 'other':
          if (!identifier) throw new Error('User ID required for other profile');
          await this.loadOtherUserProfile(identifier);
          break;
        case 'player':
          if (!identifier) throw new Error('Player name required for player profile');
          await this.loadPlayerProfile(identifier);
          break;
        default:
          throw new Error(`Unknown profile type: ${profileType}`);
      }

      console.log('✅ Unified Profile Manager initialized');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize profile manager:', error);
      throw error;
    }
  }

  /**
   * Load current user's profile (my profile)
   */
  async loadMyProfile() {
    console.log('👤 Loading my profile...');
    
    // Load user data
    const userResponse = await this.apiClient.get('/api/me');
    if (!userResponse.ok) throw new Error('Failed to load user profile');
    this.currentUser = await userResponse.json();

    // Load all players associated with this user
    await this.loadUserPlayers(this.currentUser.id);

    // Load additional data
    await Promise.all([
      this.loadUserAchievements(this.currentUser.id),
      this.loadUserTournaments(this.currentUser.id),
      this.loadUserMaps(this.currentUser.id),
      this.loadUserForumActivity(this.currentUser.id)
    ]);

    this.viewMode = 'combined';
    this.updateUI();
  }

  /**
   * Load another user's profile
   */
  async loadOtherUserProfile(userId) {
    console.log(`👥 Loading other user profile: ${userId}`);
    
    // Load user data
    const userResponse = await this.apiClient.get(`/api/users/${userId}`);
    if (!userResponse.ok) throw new Error('Failed to load user profile');
    this.currentUser = await userResponse.json();

    // Load their players
    await this.loadUserPlayers(userId);

    // Load public data only
    await Promise.all([
      this.loadUserTournaments(userId),
      this.loadUserMaps(userId),
      this.loadUserForumActivity(userId)
    ]);

    this.viewMode = 'combined';
    this.updateUI();
  }

  /**
   * Load specific player profile (may or may not be linked to a user)
   */
  async loadPlayerProfile(playerName) {
    console.log(`🎮 Loading player profile: ${playerName}`);
    
    // Load player data
    const playerResponse = await this.apiClient.get(`/api/ladder/player/${encodeURIComponent(playerName)}`);
    if (!playerResponse.ok) throw new Error('Failed to load player profile');
    const playerData = await playerResponse.json();

    // Store player data by game type
    this.currentPlayers.set(playerData.gameType, playerData);

    // If player is linked to a user, load user data too
    if (playerData.user) {
      try {
        const userResponse = await this.apiClient.get(`/api/users/${playerData.user}`);
        if (userResponse.ok) {
          this.currentUser = await userResponse.json();
          // Load other players of this user
          await this.loadUserPlayers(playerData.user);
        }
      } catch (error) {
        console.warn('Could not load user data for player:', error);
      }
    }

    this.viewMode = this.currentUser ? 'combined' : 'player';
    this.updateUI();
  }

  /**
   * Load all players associated with a user
   */
  async loadUserPlayers(userId) {
    console.log(`🎮 Loading players for user: ${userId}`);
    
    try {
      const playersResponse = await this.apiClient.get(`/api/users/${userId}/players`);
      if (playersResponse.ok) {
        const players = await playersResponse.json();
        
        // Group players by game type
        this.currentPlayers.clear();
        players.forEach(player => {
          this.currentPlayers.set(player.gameType, player);
        });
        
        console.log(`✅ Loaded ${players.length} players across ${this.currentPlayers.size} game types`);
      }
    } catch (error) {
      console.warn('Could not load user players:', error);
    }
  }

  /**
   * Load user achievements
   */
  async loadUserAchievements(userId) {
    try {
      const response = await this.apiClient.get(`/api/users/${userId}/achievements`);
      if (response.ok) {
        const achievements = await response.json();
        this.cache.set('achievements', achievements);
      }
    } catch (error) {
      console.warn('Could not load achievements:', error);
    }
  }

  /**
   * Load user tournaments
   */
  async loadUserTournaments(userId) {
    try {
      const response = await this.apiClient.get(`/api/users/${userId}/tournaments`);
      if (response.ok) {
        const tournaments = await response.json();
        this.cache.set('tournaments', tournaments);
      }
    } catch (error) {
      console.warn('Could not load tournaments:', error);
    }
  }

  /**
   * Load user maps
   */
  async loadUserMaps(userId) {
    try {
      const response = await this.apiClient.get(`/api/users/${userId}/maps`);
      if (response.ok) {
        const maps = await response.json();
        this.cache.set('maps', maps);
      }
    } catch (error) {
      console.warn('Could not load maps:', error);
    }
  }

  /**
   * Load user forum activity
   */
  async loadUserForumActivity(userId) {
    try {
      const response = await this.apiClient.get(`/api/users/${userId}/forum-activity`);
      if (response.ok) {
        const forumActivity = await response.json();
        this.cache.set('forumActivity', forumActivity);
      }
    } catch (error) {
      console.warn('Could not load forum activity:', error);
    }
  }

  /**
   * Switch view mode
   */
  setViewMode(mode) {
    if (['user', 'player', 'combined'].includes(mode)) {
      this.viewMode = mode;
      this.updateUI();
    }
  }

  /**
   * Get player data for specific game type
   */
  getPlayer(gameType) {
    return this.currentPlayers.get(gameType);
  }

  /**
   * Get all players
   */
  getAllPlayers() {
    return Array.from(this.currentPlayers.values());
  }

  /**
   * Get user data
   */
  getUser() {
    return this.currentUser;
  }

  /**
   * Get cached data
   */
  getCachedData(key) {
    return this.cache.get(key);
  }

  /**
   * Update UI based on current data and view mode
   */
  updateUI() {
    console.log(`🎨 Updating UI - Mode: ${this.viewMode}`);
    
    // Emit events for UI components to listen to
    const event = new CustomEvent('profileDataUpdated', {
      detail: {
        user: this.currentUser,
        players: this.getAllPlayers(),
        viewMode: this.viewMode,
        isOwnProfile: this.isOwnProfile,
        cache: Object.fromEntries(this.cache)
      }
    });
    
    window.dispatchEvent(event);
  }

  /**
   * Add a new player to current user
   */
  async addPlayer(playerName, gameType) {
    if (!this.isOwnProfile) {
      throw new Error('Can only add players to own profile');
    }

    try {
      const response = await this.apiClient.post('/api/ladder/players', {
        name: playerName,
        gameType: gameType
      });

      if (response.ok) {
        const newPlayer = await response.json();
        this.currentPlayers.set(gameType, newPlayer);
        this.updateUI();
        return newPlayer;
      } else {
        throw new Error('Failed to add player');
      }
    } catch (error) {
      console.error('Failed to add player:', error);
      throw error;
    }
  }

  /**
   * Remove player from current user
   */
  async removePlayer(gameType) {
    if (!this.isOwnProfile) {
      throw new Error('Can only remove players from own profile');
    }

    const player = this.currentPlayers.get(gameType);
    if (!player) {
      throw new Error('Player not found');
    }

    try {
      const response = await this.apiClient.delete(`/api/ladder/players/${player.id}`);
      
      if (response.ok) {
        this.currentPlayers.delete(gameType);
        this.updateUI();
        return true;
      } else {
        throw new Error('Failed to remove player');
      }
    } catch (error) {
      console.error('Failed to remove player:', error);
      throw error;
    }
  }

  /**
   * Update user profile data
   */
  async updateUserProfile(profileData) {
    if (!this.isOwnProfile) {
      throw new Error('Can only update own profile');
    }

    try {
      const response = await this.apiClient.put('/api/users/me', profileData);
      
      if (response.ok) {
        const updatedUser = await response.json();
        this.currentUser = { ...this.currentUser, ...updatedUser };
        this.updateUI();
        return updatedUser;
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  destroy() {
    this.cache.clear();
    this.currentPlayers.clear();
    this.currentUser = null;
  }
}

// Create singleton instance
export const unifiedProfileManager = new UnifiedProfileManager();
export default unifiedProfileManager;
