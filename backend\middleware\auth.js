// middleware/auth.js
// Legacy auth middleware - now uses unified auth system

const {
  ensureAuthenticated,
  ensureAdmin,
  ensureAdminOrModerator,
  isAuthenticated,
  isAdmin,
  optionalAuth,
  debugAuth
} = require('./unified-auth');

// Export unified auth functions for backward compatibility
exports.ensureAuthenticated = ensureAuthenticated;
exports.ensureAdmin = ensureAdmin;
exports.ensureAdminOrModerator = ensureAdminOrModerator;
exports.isAuthenticated = isAuthenticated;
exports.isAdmin = isAdmin;
exports.optionalAuth = optionalAuth;
exports.debugAuth = debugAuth;

// Middleware to check if user has set up a username
exports.ensureUsernameDefined = (req, res, next) => {
  if (req.user && req.user.isUsernameDefined) {
    return next();
  }
  res.status(403).json({ error: 'Username setup required' });
};

// Middleware to check if user is an admin
exports.isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    return next();
  }
  res.status(403).json({ error: 'Admin access required' });
};
