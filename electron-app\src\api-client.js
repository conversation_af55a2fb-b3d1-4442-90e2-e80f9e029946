/**
 * API Client for Electron App
 * 
 * Handles API communication with the backend server
 */

const axios = require('axios');

class ApiClient {
  constructor() {
    this.baseURL = 'http://127.0.0.1:3000'; // Backend server URL
    this.authToken = null;
    
    // Create axios instance
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'X-Electron-Client': 'true'
      }
    });
    
    // Setup request interceptor to add auth token
    this.client.interceptors.request.use((config) => {
      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }
      return config;
    });
    
    // Setup response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Set authentication token
   */
  setAuthToken(token) {
    this.authToken = token;
  }

  /**
   * Clear authentication token
   */
  clearAuthToken() {
    this.authToken = null;
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    try {
      const response = await this.client.get(endpoint, { params });
      return {
        ok: true,
        status: response.status,
        data: response.data,
        json: () => Promise.resolve(response.data)
      };
    } catch (error) {
      return {
        ok: false,
        status: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        json: () => Promise.resolve(error.response?.data || { error: error.message })
      };
    }
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}) {
    try {
      const response = await this.client.post(endpoint, data);
      return {
        ok: true,
        status: response.status,
        data: response.data,
        json: () => Promise.resolve(response.data)
      };
    } catch (error) {
      return {
        ok: false,
        status: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        json: () => Promise.resolve(error.response?.data || { error: error.message })
      };
    }
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}) {
    try {
      const response = await this.client.put(endpoint, data);
      return {
        ok: true,
        status: response.status,
        data: response.data,
        json: () => Promise.resolve(response.data)
      };
    } catch (error) {
      return {
        ok: false,
        status: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        json: () => Promise.resolve(error.response?.data || { error: error.message })
      };
    }
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    try {
      const response = await this.client.delete(endpoint);
      return {
        ok: true,
        status: response.status,
        data: response.data,
        json: () => Promise.resolve(response.data)
      };
    } catch (error) {
      return {
        ok: false,
        status: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        json: () => Promise.resolve(error.response?.data || { error: error.message })
      };
    }
  }

  /**
   * Test connection to backend
   */
  async testConnection() {
    try {
      const response = await this.get('/api/health');
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Update base URL
   */
  setBaseURL(url) {
    this.baseURL = url;
    this.client.defaults.baseURL = url;
  }
}

module.exports = { ApiClient };
