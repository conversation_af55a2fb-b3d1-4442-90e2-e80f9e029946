/**
 * Dashboard Preload Script
 * 
 * Handles communication between the main process and the dashboard renderer
 */

const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Game detection events
  onGameStarted: (callback) => ipcRenderer.on('game-started', callback),
  onGameEnded: (callback) => ipcRenderer.on('game-ended', callback),
  onResultDetected: (callback) => ipcRenderer.on('result-detected', callback),
  onGameResult: (callback) => ipcRenderer.on('game-result', callback),
  onAutoReported: (callback) => ipcRenderer.on('auto-reported', callback),
  onAutoReportFailed: (callback) => ipcRenderer.on('auto-report-failed', callback),
  
  // Dashboard actions
  openArenaWebsite: () => ipcRenderer.invoke('open-arena-website'),
  openManualReport: () => ipcRenderer.invoke('open-manual-report'),
  getStoredUser: () => ipcRenderer.invoke('get-stored-user'),
  getGameStats: () => ipcRenderer.invoke('get-game-stats'),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  updateSettings: (settings) => ipcRenderer.invoke('update-settings', settings),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Dashboard API for the renderer
contextBridge.exposeInMainWorld('dashboardAPI', {
  // Initialize dashboard
  init: () => {
    console.log('🎮 Dashboard API initialized');
    return Promise.resolve();
  },
  
  // Get current user
  getCurrentUser: async () => {
    try {
      return await ipcRenderer.invoke('get-stored-user');
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  },
  
  // Get game statistics
  getGameStats: async () => {
    try {
      return await ipcRenderer.invoke('get-game-stats');
    } catch (error) {
      console.error('Failed to get game stats:', error);
      return {
        warcraft1: { played: 0, won: 0, lost: 0 },
        warcraft2: { played: 0, won: 0, lost: 0 },
        warcraft3: { played: 0, won: 0, lost: 0 }
      };
    }
  },
  
  // Show login
  showLogin: () => {
    ipcRenderer.invoke('open-arena-website');
  },
  
  // Show manual report
  showManualReport: (gameSession) => {
    ipcRenderer.invoke('open-manual-report', gameSession);
  },
  
  // Utility functions
  formatTime: (timestamp) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'just now';
    if (minutes === 1) return '1 minute ago';
    return `${minutes} minutes ago`;
  },
  
  getGameTitle: (gameType) => {
    const titles = {
      'warcraft1': 'Warcraft I',
      'warcraft2': 'Warcraft II',
      'warcraft3': 'Warcraft III'
    };
    return titles[gameType] || gameType;
  }
});

// Global dashboard state
window.dashboardState = {
  currentUser: null,
  gameStats: {
    warcraft1: { played: 0, won: 0, lost: 0 },
    warcraft2: { played: 0, won: 0, lost: 0 },
    warcraft3: { played: 0, won: 0, lost: 0 }
  },
  currentGameSession: null,
  recentActivity: []
};

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🎮 Dashboard preload: DOM loaded');
  
  try {
    // Load initial data
    window.dashboardState.currentUser = await window.dashboardAPI.getCurrentUser();
    window.dashboardState.gameStats = await window.dashboardAPI.getGameStats();
    
    // Initialize UI
    updateUserDisplay();
    updateStatsDisplay();
    updateGameStatusDisplay();
    
    // Setup event listeners
    setupEventListeners();
    
    console.log('✅ Dashboard initialized successfully');
  } catch (error) {
    console.error('❌ Dashboard initialization failed:', error);
  }
});

// UI Update Functions
function updateUserDisplay() {
  const userElement = document.getElementById('current-user');
  if (!userElement) return;
  
  const user = window.dashboardState.currentUser;
  
  if (user) {
    userElement.innerHTML = `
      <div class="user-info">
        <img src="${user.avatar || '../frontend/assets/img/ranks/emblem.png'}" alt="Avatar" class="user-avatar">
        <div class="user-details">
          <div class="username">${user.username}</div>
          <div class="user-level">Level ${user.achievementLevel || 1}</div>
        </div>
      </div>
    `;
  } else {
    userElement.innerHTML = `
      <div class="login-prompt">
        <button onclick="window.dashboardAPI.showLogin()" class="btn btn-primary">
          Login to Arena
        </button>
      </div>
    `;
  }
}

function updateStatsDisplay() {
  const statsElement = document.getElementById('game-stats');
  if (!statsElement) return;
  
  const stats = window.dashboardState.gameStats;
  
  const statsHTML = Object.entries(stats).map(([gameType, gameStats]) => {
    const winRate = gameStats.played > 0 ? Math.round((gameStats.won / gameStats.played) * 100) : 0;
    const gameTitle = window.dashboardAPI.getGameTitle(gameType);
    
    return `
      <div class="game-stat-card">
        <div class="game-title">${gameTitle}</div>
        <div class="stat-row">
          <span class="stat-label">Played:</span>
          <span class="stat-value">${gameStats.played}</span>
        </div>
        <div class="stat-row">
          <span class="stat-label">Won:</span>
          <span class="stat-value won">${gameStats.won}</span>
        </div>
        <div class="stat-row">
          <span class="stat-label">Lost:</span>
          <span class="stat-value lost">${gameStats.lost}</span>
        </div>
        <div class="stat-row">
          <span class="stat-label">Win Rate:</span>
          <span class="stat-value">${winRate}%</span>
        </div>
      </div>
    `;
  }).join('');
  
  statsElement.innerHTML = statsHTML;
}

function updateGameStatusDisplay(status = { status: 'idle' }) {
  const statusElement = document.getElementById('game-status');
  if (!statusElement) return;
  
  if (status.status === 'playing') {
    statusElement.innerHTML = `
      <div class="status-playing">
        <div class="status-indicator playing"></div>
        <div class="status-text">
          <div class="game-name">${status.game}</div>
          <div class="game-time">Started ${window.dashboardAPI.formatTime(status.startTime)}</div>
        </div>
      </div>
    `;
  } else {
    statusElement.innerHTML = `
      <div class="status-idle">
        <div class="status-indicator idle"></div>
        <div class="status-text">
          <div class="game-name">Ready to play</div>
          ${status.lastGame ? `<div class="last-game">Last: ${status.lastGame}</div>` : ''}
        </div>
      </div>
    `;
  }
}

function addRecentActivity(message, type = 'info') {
  const activityElement = document.getElementById('recent-activity');
  if (!activityElement) return;
  
  const activity = {
    message: message,
    type: type,
    timestamp: Date.now()
  };
  
  window.dashboardState.recentActivity.unshift(activity);
  
  // Keep only last 10 activities
  if (window.dashboardState.recentActivity.length > 10) {
    window.dashboardState.recentActivity = window.dashboardState.recentActivity.slice(0, 10);
  }
  
  // Update display
  const activitiesHTML = window.dashboardState.recentActivity.map(activity => `
    <div class="activity-item">
      <span>${activity.message}</span>
      <span class="activity-time">${window.dashboardAPI.formatTime(activity.timestamp)}</span>
    </div>
  `).join('');
  
  activityElement.innerHTML = activitiesHTML;
}

function updateDetectionStatus(status) {
  const indicators = {
    'process-monitor-status': status.processMonitor || false,
    'log-monitor-status': status.logMonitor || false,
    'replay-analyzer-status': status.replayAnalyzer || false
  };
  
  for (const [id, active] of Object.entries(indicators)) {
    const element = document.getElementById(id);
    if (element) {
      element.className = `detection-status-indicator ${active ? '' : 'inactive'}`;
    }
  }
}

// Event Listeners Setup
function setupEventListeners() {
  // Game started
  window.electronAPI.onGameStarted((event, gameSession) => {
    console.log('🎮 Game started:', gameSession);
    
    window.dashboardState.currentGameSession = gameSession;
    updateGameStatusDisplay({
      status: 'playing',
      game: gameSession.name,
      startTime: gameSession.startTime
    });
    
    addRecentActivity(`Started playing ${gameSession.name}`, 'game');
  });
  
  // Game ended
  window.electronAPI.onGameEnded((event, gameSession) => {
    console.log('🎮 Game ended:', gameSession);
    
    window.dashboardState.currentGameSession = null;
    updateGameStatusDisplay({
      status: 'idle',
      lastGame: gameSession.name
    });
    
    addRecentActivity(`Finished playing ${gameSession.name}`, 'game');
  });
  
  // Result detected
  window.electronAPI.onResultDetected((event, data) => {
    console.log('🎯 Result detected:', data);
    
    addRecentActivity(`${data.outcome.toUpperCase()} detected via ${data.method}`, 'result');
  });
  
  // Final game result
  window.electronAPI.onGameResult((event, gameSession) => {
    console.log('✅ Final game result:', gameSession);
    
    const result = gameSession.finalResult;
    addRecentActivity(`${result.outcome.toUpperCase()} in ${gameSession.name} (${Math.round(result.confidence * 100)}% confidence)`, 'result');
    
    // Update local stats
    const gameType = gameSession.type;
    if (window.dashboardState.gameStats[gameType]) {
      window.dashboardState.gameStats[gameType].played++;
      if (result.outcome === 'victory') {
        window.dashboardState.gameStats[gameType].won++;
      } else if (result.outcome === 'defeat') {
        window.dashboardState.gameStats[gameType].lost++;
      }
      updateStatsDisplay();
    }
  });
  
  // Auto-reported
  window.electronAPI.onAutoReported((event, data) => {
    console.log('📤 Auto-reported:', data);
    
    addRecentActivity(`Match auto-reported to Arena`, 'success');
  });
  
  // Auto-report failed
  window.electronAPI.onAutoReportFailed((event, data) => {
    console.log('❌ Auto-report failed:', data);
    
    addRecentActivity(`Auto-report failed: ${data.error}`, 'error');
  });
}

// Initialize detection status as active
updateDetectionStatus({
  processMonitor: true,
  logMonitor: true,
  replayAnalyzer: true
});

// Add initial activity
addRecentActivity('Arena Core Dashboard started', 'info');
