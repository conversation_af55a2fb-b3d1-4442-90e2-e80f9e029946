/**
 * Fix Email Index Script
 * 
 * Fixes the MongoDB unique index issue with email field that's preventing
 * profile image updates from being saved properly.
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function fixEmailIndex() {
  try {
    console.log('🔧 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/newsite');
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    console.log('🔍 Checking current indexes...');
    const indexes = await usersCollection.indexes();
    console.log('Current indexes:', indexes.map(idx => ({ name: idx.name, key: idx.key })));

    // Check if the problematic email index exists
    const emailIndex = indexes.find(idx => idx.key && idx.key.email === 1);
    
    if (emailIndex) {
      console.log('🗑️ Dropping problematic email index...');
      try {
        await usersCollection.dropIndex('email_1');
        console.log('✅ Dropped email_1 index');
      } catch (error) {
        console.log('⚠️ Index might not exist or already dropped:', error.message);
      }
    }

    console.log('🔧 Creating new sparse email index...');
    await usersCollection.createIndex(
      { email: 1 }, 
      { 
        unique: true, 
        sparse: true, // This allows multiple null values
        name: 'email_sparse_unique'
      }
    );
    console.log('✅ Created new sparse email index');

    console.log('🔍 Checking for users with null emails...');
    const nullEmailCount = await usersCollection.countDocuments({ email: null });
    console.log(`📊 Found ${nullEmailCount} users with null emails`);

    if (nullEmailCount > 0) {
      console.log('🔧 Updating users with null emails...');
      const result = await usersCollection.updateMany(
        { email: null },
        { $unset: { email: "" } } // Remove the email field entirely for null values
      );
      console.log(`✅ Updated ${result.modifiedCount} users`);
    }

    console.log('🔍 Final index check...');
    const finalIndexes = await usersCollection.indexes();
    console.log('Final indexes:', finalIndexes.map(idx => ({ name: idx.name, key: idx.key, sparse: idx.sparse, unique: idx.unique })));

    console.log('✅ Email index fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing email index:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the fix
if (require.main === module) {
  fixEmailIndex().then(() => {
    console.log('🎉 Script completed');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

module.exports = fixEmailIndex;
