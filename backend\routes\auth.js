const express = require('express');
const passport = require('passport');
const { availableStrategies } = require('../config/passport');
const router = express.Router();
const authController = require('../controllers/authController');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// Use unified authentication middleware
const { ensureJWTAuth, ensureAuthenticated } = require('../middleware/unified-auth');

// Debug middleware for auth routes
router.use((req, res, next) => {
  const userAgent = req.get('User-Agent') || '';
  const electronHeader = req.get('X-Electron-App');
  const isElectron = userAgent.includes('Electron') || 
                     userAgent.includes('WCArenaCompanion') || 
                     electronHeader === 'true';
  
  console.log('Auth Route Debug:', {
    path: req.path,
    method: req.method,
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
    user: req.user ? {
      id: req.user._id,
      username: req.user.username
    } : null,
    isElectron: isElectron,
    electronDetection: {
      userAgent: userAgent.substring(0, 100),
      header: electronHeader,
      isElectron: isElectron
    }
  });
  next();
});

// OAuth configuration endpoint for Electron app
router.get('/config', (req, res) => {
  console.log('⚙️ OAuth config request received');
  
  try {
    const config = {
      availableStrategies: availableStrategies,
      serverOnline: true,
      endpoints: {
        google: availableStrategies.google ? '/auth/google' : null,
        discord: availableStrategies.discord ? '/auth/discord' : null,
        twitch: availableStrategies.twitch ? '/auth/twitch' : null
      }
    };
    
    console.log('✅ Sending OAuth config:', config);
    res.json(config);
  } catch (error) {
    console.error('❌ Error getting OAuth config:', error);
    res.status(500).json({ 
      error: 'Failed to get OAuth configuration',
      availableStrategies: {},
      serverOnline: false
    });
  }
});

// Debug endpoint to check OAuth status
router.get('/debug/oauth-status', (req, res) => {
  console.log('🔍 OAuth Debug Request');
  
  try {
    const envVars = {
      GOOGLE_CLIENT_ID: !!process.env.GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET: !!process.env.GOOGLE_CLIENT_SECRET,
      DISCORD_CLIENT_ID: !!process.env.DISCORD_CLIENT_ID,
      DISCORD_CLIENT_SECRET: !!process.env.DISCORD_CLIENT_SECRET,
      TWITCH_CLIENT_ID: !!process.env.TWITCH_CLIENT_ID,
      TWITCH_CLIENT_SECRET: !!process.env.TWITCH_CLIENT_SECRET
    };
    
    console.log('🔍 Environment variables status:', envVars);
    console.log('🔍 Available strategies:', availableStrategies);
    
    res.json({
      environment: envVars,
      availableStrategies: availableStrategies,
      passportStrategies: Object.keys(require('passport')._strategies || {}),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error in OAuth debug:', error);
    res.status(500).json({ 
      error: 'Failed to get OAuth debug info',
      message: error.message
    });
  }
});

// Helper middleware to check if strategy is available
function checkStrategy(strategyName) {
  return (req, res, next) => {
    console.log(`🔍 Checking strategy: ${strategyName}`, {
      available: !!availableStrategies[strategyName],
      allStrategies: availableStrategies,
      isElectron: req.query.electron === 'true' || req.headers['x-electron-client'] === 'true'
    });
    
    if (!availableStrategies[strategyName]) {
      console.error(`❌ Strategy ${strategyName} not available. Available strategies:`, Object.keys(availableStrategies).filter(key => availableStrategies[key]));
      
      // For Electron, provide a more user-friendly error
      const isElectron = req.query.electron === 'true' || req.headers['x-electron-client'] === 'true';
      
      if (isElectron) {
        // Redirect to an error page instead of returning JSON for Electron
        return res.redirect(`/views/login.html?error=oauth_not_configured&provider=${strategyName}`);
      }
      
      return res.status(503).json({
        error: `${strategyName.charAt(0).toUpperCase() + strategyName.slice(1)} OAuth is not configured`,
        message: `Please configure ${strategyName.toUpperCase()}_CLIENT_ID and ${strategyName.toUpperCase()}_CLIENT_SECRET environment variables to enable ${strategyName} authentication.`,
        availableStrategies: Object.keys(availableStrategies).filter(key => availableStrategies[key]),
        provider: strategyName
      });
    }
    
    console.log(`✅ Strategy ${strategyName} is available, proceeding with OAuth`);
    next();
  };
}

// OAuth login redirects
router.get('/google', checkStrategy('google'), (req, res, next) => {
  // Enhanced Electron detection with fallbacks
  const queryElectron = req.query.electron === 'true' || req.query.embedded === 'true';
  const userAgent = req.get('User-Agent') || '';
  const userAgentElectron = userAgent.includes('Electron');
  const headerElectron = req.get('X-Electron-App') === 'true';
  
  // Use query param first, then user agent as fallback
  const isElectron = queryElectron || userAgentElectron || headerElectron;
  const clientState = req.query.state || '';
  
  console.log('🔵 === GOOGLE OAUTH START ===');
  console.log('🔍 Query params:', req.query);
  console.log('🔍 Enhanced Electron detection:', {
    queryElectron,
    userAgentElectron,
    headerElectron,
    finalIsElectron: isElectron,
    userAgent: userAgent.substring(0, 100) + '...'
  });
  console.log('🔍 Client state parameter:', clientState);
  console.log('🔍 Session ID:', req.sessionID);
  console.log('=================================');
  
  // Create OAuth state that includes both client state and electron flag
  const oauthState = JSON.stringify({
    clientState: clientState,
    isElectron: isElectron
  });
  
  console.log('🔍 OAuth state being sent:', oauthState);
  console.log('🚀 Redirecting to Google OAuth...');
  
  // Use passport.authenticate as middleware (correct for OAuth providers)
  passport.authenticate('google', { 
    scope: ['openid', 'profile', 'email'],
    prompt: 'select_account',
    state: oauthState
  })(req, res, next);
});

router.get('/discord', checkStrategy('discord'), (req, res, next) => {
  // Enhanced Electron detection with fallbacks
  const queryElectron = req.query.electron === 'true' || req.query.embedded === 'true';
  const userAgent = req.get('User-Agent') || '';
  const userAgentElectron = userAgent.includes('Electron');
  const headerElectron = req.get('X-Electron-App') === 'true';
  
  // Use query param first, then user agent as fallback
  const isElectron = queryElectron || userAgentElectron || headerElectron;
  const state = req.query.state;
  
  console.log('🟣 === DISCORD OAUTH START ===');
  console.log('🔍 Enhanced Electron detection:', {
    queryElectron,
    userAgentElectron,
    headerElectron,
    finalIsElectron: isElectron
  });
  
  if (isElectron) {
    req.session.isElectron = true;
    req.session.oauthState = state;
  }
  
  passport.authenticate('discord', { state: state })(req, res, next);
});

router.get('/twitch', checkStrategy('twitch'), (req, res, next) => {
  // Enhanced Electron detection with fallbacks
  const queryElectron = req.query.electron === 'true' || req.query.embedded === 'true';
  const userAgent = req.get('User-Agent') || '';
  const userAgentElectron = userAgent.includes('Electron');
  const headerElectron = req.get('X-Electron-App') === 'true';
  
  // Use query param first, then user agent as fallback
  const isElectron = queryElectron || userAgentElectron || headerElectron;
  const state = req.query.state;
  
  console.log('🟡 === TWITCH OAUTH START ===');
  console.log('🔍 Enhanced Electron detection:', {
    queryElectron,
    userAgentElectron,
    headerElectron,
    finalIsElectron: isElectron
  });
  
  if (isElectron) {
    req.session.isElectron = true;
    req.session.oauthState = state;
  }
  
  passport.authenticate('twitch', { state: state })(req, res, next);
});

// OAuth callbacks
router.get('/google/callback',
  checkStrategy('google'),
  passport.authenticate('google', { 
    failureRedirect: '/login?error=google_auth_failed',
    failureMessage: true
  }),
  authController.handleOAuthCallback
);

router.get('/discord/callback',
  checkStrategy('discord'),
  passport.authenticate('discord', { 
    failureRedirect: '/login?error=discord_auth_failed',
    failureMessage: true
  }),
  authController.handleOAuthCallback
);

router.get('/twitch/callback',
  checkStrategy('twitch'),
  passport.authenticate('twitch', { 
    failureRedirect: '/login?error=twitch_auth_failed',
    failureMessage: true
  }),
  authController.handleOAuthCallback
);

// Electron-specific routes
router.get('/validate-user', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const isElectron = req.headers['x-electron-client'] === 'true';
    
    if (!userId || !isElectron) {
      return res.status(400).json({ success: false, error: 'Invalid request' });
    }
    
    const User = require('../models/User');
    const user = await User.findById(userId).select('-password');
    
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }
    
    // Update last login
    user.lastLogin = new Date();
    await user.save();
    
    res.json({ 
      success: true, 
      user: {
        _id: user._id,
        id: user._id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        role: user.role || 'user',
        isUsernameDefined: user.isUsernameDefined
      }
    });
  } catch (error) {
    console.error('User validation error:', error);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

router.get('/electron/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const isElectron = req.headers['x-electron-client'] === 'true';
    
    if (!token || !isElectron) {
      return res.status(400).json({ success: false, error: 'Invalid request' });
    }
    
    // Verify the token (implement your token verification logic)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    const User = require('../models/User');
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }
    
    res.json({ 
      success: true, 
      user: {
        _id: user._id,
        id: user._id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        role: user.role || 'user',
        isUsernameDefined: user.isUsernameDefined
      }
    });
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
});

router.post('/electron/logout', (req, res) => {
  try {
    const isElectron = req.headers['x-electron-app'] === 'true';
    
    if (!isElectron) {
      return res.status(400).json({ success: false, error: 'Invalid request' });
    }
    
    // Perform any server-side logout cleanup if needed
    console.log('Electron client logout notification received');
    
    res.json({ success: true, message: 'Logout notification received' });
  } catch (error) {
    console.error('Electron logout error:', error);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// Clear browser sessions for Electron logout (with session isolation)
router.post('/logout/clear-sessions', (req, res) => {
  try {
    const isElectron = req.headers['x-electron-client'] === 'true';
    const sessionIsolation = req.headers['x-session-isolation'] === 'true';
    
    console.log('🧹 Session clearing request received:', {
      isElectron,
      sessionIsolation,
      userAgent: req.get('User-Agent'),
      sessionID: req.sessionID
    });
    
    if (sessionIsolation && isElectron) {
      // For session isolation, only clear Electron-specific data
      console.log('🔒 Using session isolation - clearing only Electron sessions');
      
      // Don't destroy the main session, just clear browser-specific cookies
      // This allows browser and Electron to coexist
    } else {
      // Legacy behavior - destroy all sessions
      if (req.session) {
        req.session.destroy((err) => {
          if (err) {
            console.log('⚠️ Session destroy error (non-critical):', err);
          } else {
            console.log('✅ Session destroyed during clear-sessions');
          }
        });
      }
    }
    
    // Clear all authentication-related cookies with comprehensive options
    const cookiesToClear = [
      'connect.sid', 'sessionId', 'authToken', 'token', 'jwt', 'session',
      'oauth_token', 'oauth_state', 'passport_session', 'express.sid'
    ];
    
    cookiesToClear.forEach(cookieName => {
      // Clear with various possible configurations
      res.clearCookie(cookieName);
      res.clearCookie(cookieName, { path: '/' });
      res.clearCookie(cookieName, { path: '/', domain: 'localhost' });
      res.clearCookie(cookieName, { path: '/', domain: '127.0.0.1' });
      res.clearCookie(cookieName, { path: '/', httpOnly: true });
      res.clearCookie(cookieName, { path: '/', secure: false });
      res.clearCookie(cookieName, { path: '/', sameSite: 'lax' });
    });

    // Set cookies to expired explicitly with all domain variations
    const domains = ['', 'localhost', '127.0.0.1'];
    domains.forEach(domain => {
      const options = { expires: new Date(0), path: '/' };
      if (domain) options.domain = domain;
      
      cookiesToClear.forEach(cookieName => {
        res.cookie(cookieName, '', options);
      });
    });

    console.log('🧹 Comprehensive cookie and session cleanup completed');
    
    // Add cache control headers to prevent caching
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });
    
    res.json({ 
      success: true, 
      message: 'Browser sessions and cookies cleared successfully',
      cleared: cookiesToClear.length + ' cookie types cleared'
    });
    
  } catch (error) {
    console.error('❌ Session clearing error:', error);
    res.status(500).json({ success: false, error: 'Failed to clear sessions' });
  }
});

// Auth status check - always fetch fresh data from database
router.get('/me', async (req, res) => {
  console.log('Auth /me Debug:', {
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
    user: req.user ? {
      id: req.user._id,
      username: req.user.username,
      sessionRole: req.user.role
    } : null
  });

  if (req.isAuthenticated && req.isAuthenticated()) {
    try {
      // Always fetch fresh user data from database to avoid stale session data
      const User = require('../models/User');
      const freshUser = await User.findById(req.user._id).select('-password').lean();

      if (!freshUser) {
        console.log('User not found in database');
        return res.status(404).json({ error: 'User not found' });
      }

      // Return complete fresh user data
      const userData = {
        _id: freshUser._id,
        id: freshUser._id, // Include both _id and id for compatibility
        username: freshUser.username,
        displayName: freshUser.displayName,
        email: freshUser.email,
        avatar: freshUser.avatar,
        isUsernameDefined: freshUser.isUsernameDefined,
        role: freshUser.role || 'user', // Fresh role from database
        // Include any other fields that might be needed
        ...(freshUser.socialLinks && { socialLinks: freshUser.socialLinks }),
        ...(freshUser.bio && { bio: freshUser.bio }),
        ...(freshUser.createdAt && { createdAt: freshUser.createdAt }),
        ...(freshUser.profile && { profile: freshUser.profile }),
        ...(freshUser.avatarPreferences && { avatarPreferences: freshUser.avatarPreferences })
      };

      console.log('🔍 Fresh user data from database:', {
        username: userData.username,
        sessionRole: req.user.role,
        databaseRole: freshUser.role,
        finalRole: userData.role
      });

      // SPECIAL DEBUG FOR TURTLEMAN
      if (userData.username === 'TURTLEMAN' || userData.username === 'turtleman') {
        console.log('🐢 BACKEND TURTLEMAN DEBUG:', {
          username: userData.username,
          sessionRole: req.user.role,
          databaseRole: freshUser.role,
          finalRole: userData.role,
          roleType: typeof freshUser.role,
          isAdmin: freshUser.role === 'admin',
          fullDatabaseUser: freshUser
        });
      }

      res.json(userData);
    } catch (error) {
      console.error('Error fetching fresh user data:', error);
      res.status(500).json({ error: 'Server error' });
    }
  } else {
    console.log('User not authenticated');
    res.status(401).json({ error: 'Not authenticated' });
  }
});

// Debug endpoint to make current user admin (temporary for debugging)
router.post('/make-admin', async (req, res) => {
  try {
    if (!req.isAuthenticated || !req.isAuthenticated()) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const User = require('../models/User');
    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('🔍 Current user before update:', {
      username: user.username,
      role: user.role,
      id: user._id
    });

    // Make the current user an admin
    user.role = 'admin';
    await user.save();

    // Update the session user object
    req.user.role = 'admin';

    console.log('✅ User updated to admin:', {
      username: user.username,
      role: user.role,
      id: user._id
    });

    res.json({
      message: 'User role updated to admin',
      user: {
        username: user.username,
        role: user.role,
        id: user._id
      }
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST username setup
router.post('/setup-username', authController.setupUsername);

// Logout route
router.get('/logout', (req, res) => {
  console.log('🚪 LOGOUT REQUEST RECEIVED');
  console.log('🔍 LOGOUT DEBUG: Request details:', {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated ? req.isAuthenticated() : 'N/A',
    user: req.user ? req.user.username : null,
    headers: {
      'X-Electron-App': req.get('X-Electron-App'),
      'Accept': req.get('Accept'),
      'Referer': req.get('Referer')
    },
    cookies: req.cookies
  });

  // Check if request is from Electron app
  const userAgent = req.get('User-Agent') || '';
  const electronHeader = req.get('X-Electron-App');
  const isElectron = userAgent.includes('Electron') || 
                     userAgent.includes('WCArenaCompanion') || 
                     electronHeader === 'true';

  console.log('🔍 LOGOUT DEBUG: Electron detection:', {
    userAgent: userAgent.substring(0, 100) + '...',
    electronHeader,
    isElectron
  });

  // Store session info before destruction
  const sessionInfoBeforeLogout = {
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated ? req.isAuthenticated() : false,
    user: req.user ? req.user.username : null
  };

  console.log('🔍 LOGOUT DEBUG: Session before logout:', sessionInfoBeforeLogout);

  // Perform logout
  req.logout((err) => {
    if (err) {
      console.error('❌ LOGOUT ERROR during req.logout():', err);
      return res.status(500).json({ success: false, error: 'Logout failed' });
    }

    console.log('✅ LOGOUT: req.logout() completed successfully');

    // Destroy session
    req.session.destroy((sessionErr) => {
      if (sessionErr) {
        console.error('❌ LOGOUT ERROR during session.destroy():', sessionErr);
        // Continue anyway
      } else {
        console.log('✅ LOGOUT: Session destroyed successfully');
      }

             // Clear ALL authentication-related cookies with comprehensive options
       const cookiesToClear = ['connect.sid', 'sessionId', 'authToken', 'token', 'jwt', 'session'];
       cookiesToClear.forEach(cookieName => {
         // Clear with various possible configurations
         res.clearCookie(cookieName);
         res.clearCookie(cookieName, { path: '/' });
         res.clearCookie(cookieName, { path: '/', domain: 'localhost' });
         res.clearCookie(cookieName, { path: '/', httpOnly: true });
         res.clearCookie(cookieName, { path: '/', secure: false });
       });

       // Also set cookies to expired explicitly
       res.cookie('authToken', '', { expires: new Date(0), path: '/' });
       res.cookie('sessionId', '', { expires: new Date(0), path: '/' });
       res.cookie('connect.sid', '', { expires: new Date(0), path: '/' });

       console.log('🧹 LOGOUT: All authentication cookies cleared with multiple configurations');

      // Check session state after logout
      const sessionInfoAfterLogout = {
        sessionID: req.sessionID,
        isAuthenticated: req.isAuthenticated ? req.isAuthenticated() : false,
        user: req.user ? req.user.username : null
      };

      console.log('🔍 LOGOUT DEBUG: Session after logout:', sessionInfoAfterLogout);

      if (isElectron) {
        console.log('🔐 LOGOUT: Sending JSON response for Electron app');
        const responseData = { 
          success: true, 
          message: 'Logged out successfully',
          debug: {
            beforeLogout: sessionInfoBeforeLogout,
            afterLogout: sessionInfoAfterLogout
          }
        };
        console.log('🔍 LOGOUT DEBUG: Sending response:', responseData);
        res.json(responseData);
      } else {
        console.log('🌐 LOGOUT: Redirecting web browser to login page');
        res.redirect('/views/login.html');
      }
    });
  });
});

// Endpoint to transfer Electron session to web browser - simplified JWT approach
router.post('/transfer-session', ensureJWTAuth, async (req, res) => {
  try {
    console.log('🔄 Session transfer request from:', req.user.username);
    
    // Generate a JWT transfer token (valid for 5 minutes)
    const transferToken = jwt.sign(
      {
        userId: req.user.userId,
        username: req.user.username,
        type: 'transfer'
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '5m' }
    );
    
    console.log('🎫 Generated JWT transfer token for user:', req.user.username);
    
    res.json({ 
      success: true, 
      transferToken,
      expiresIn: 300 // 5 minutes in seconds
    });
    
  } catch (error) {
    console.error('❌ Session transfer error:', error);
    res.status(500).json({ success: false, error: 'Session transfer failed' });
  }
});

// Endpoint to claim transfer token and set JWT cookie
router.post('/claim-transfer', async (req, res) => {
  try {
    const { transferToken } = req.body;

    if (!transferToken) {
      return res.status(400).json({ success: false, error: 'Transfer token required' });
    }

    console.log('🎯 Transfer token claim attempt');

    // Find and validate the transfer token
    const decoded = jwt.verify(transferToken, process.env.JWT_SECRET || 'your-secret-key');
    
    if (decoded.type !== 'transfer') {
      return res.status(400).json({ success: false, error: 'Invalid token type' });
    }

    // Get the user
    const User = require('../models/User');
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    // Generate a new JWT token for web authentication
    const webToken = jwt.sign(
      { 
        userId: user._id,
        username: user.username,
        type: 'web'
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    console.log('✅ Transfer token claimed successfully for user:', user.username);

    // Instead of cookies, return the JWT token directly for localStorage storage
    res.json({ 
      success: true, 
      authToken: webToken, // Send token directly to frontend
      user: {
        id: user._id,
        username: user.username
      }
    });

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(400).json({ success: false, error: 'Transfer token expired' });
    }
    
    console.error('Transfer claim error:', error);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// Success page for embedded browser OAuth
router.get('/electron/success', (req, res) => {
  const token = req.query.token;
  const state = req.query.state;
  const userAgent = req.get('User-Agent') || '';
  const isEmbeddedBrowser = userAgent.includes('WCArenaCompanion') || 
                           userAgent.includes('Electron') || 
                           userAgent.includes('WC_Arena_Core') ||
                           req.query.embedded === 'true' ||
                           req.query.electron === 'true';
  
  console.log('✅ Embedded browser success page accessed', { isEmbeddedBrowser, userAgent: userAgent.substring(0, 100) });
  
  if (!token) {
    return res.status(400).send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>OAuth Error - WC Arena</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            text-align: center;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>❌ Login Error</h1>
          <p>No authentication token provided</p>
        </div>
      </body>
      </html>
    `);
  }
  
  // Handle embedded browser (Electron) vs regular browser differently
  if (isEmbeddedBrowser) {
    console.log('🖥️ Handling embedded browser OAuth success');
    // For embedded browsers, show simple success page and let the browser extract the token from URL
    return res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Login Success - WC Arena</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            text-align: center;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 400px;
          }
          .success-icon {
            font-size: 64px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
          }
          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="success-icon">🎉</div>
          <h1>Login Successful!</h1>
          <p>You are now logged in to WC Arena.</p>
          <p>This window will close automatically...</p>
        </div>
        <script>
          // Store token data for Electron to access
          window.oauthResult = {
            success: true,
            token: '${token}',
            state: '${state}',
            timestamp: ${Date.now()}
          };
          
          // Signal that the page is ready
          console.log('OAuth success page loaded with token');
        </script>
      </body>
      </html>
    `);
  }
  
  // Generate a simple 6-digit login code
  const loginCode = Math.random().toString(36).substr(2, 6).toUpperCase();
  
  // Store the token temporarily with the code (expires in 5 minutes)
  const tempStore = req.app.get('tempTokenStore') || new Map();
  tempStore.set(loginCode, {
    token: token,
    timestamp: Date.now(),
    expires: Date.now() + 5 * 60 * 1000 // 5 minutes
  });
  req.app.set('tempTokenStore', tempStore);
  
  console.log(`🎫 Generated login code: ${loginCode} for token`);
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Login Success - WC Arena</title>
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          margin: 0;
          text-align: center;
        }
        .container {
          background: rgba(255, 255, 255, 0.1);
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          max-width: 500px;
          width: 90%;
        }
        .code {
          background: rgba(255, 255, 255, 0.2);
          padding: 20px;
          border-radius: 10px;
          font-size: 32px;
          font-weight: bold;
          letter-spacing: 3px;
          margin: 20px 0;
          border: 2px dashed rgba(255, 255, 255, 0.5);
          cursor: pointer;
          transition: all 0.3s ease;
        }
        .code:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.05);
        }
        .copy-btn {
          background: #2196F3;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 16px;
          cursor: pointer;
          margin: 10px;
          transition: background 0.3s ease;
        }
        .copy-btn:hover {
          background: #1976D2;
        }
        .instructions {
          margin: 20px 0;
          font-size: 18px;
          line-height: 1.5;
        }
        .step {
          background: rgba(255, 255, 255, 0.1);
          padding: 15px;
          margin: 10px 0;
          border-radius: 8px;
          border-left: 4px solid #FFD700;
        }
        .success-icon {
          font-size: 48px;
          margin-bottom: 20px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="success-icon">🎉</div>
        <h1>Login Successful!</h1>
        
        <div class="instructions">
          <div class="step">
            <strong>Step 1:</strong> Copy this login code
          </div>
        </div>
        
        <div class="code" id="loginCode" onclick="copyCode()">${loginCode}</div>
        
        <button class="copy-btn" onclick="copyCode()">📋 Copy Code</button>
        
        <div class="instructions">
          <div class="step">
            <strong>Step 2:</strong> Go back to WC Arena Companion
          </div>
          <div class="step">
            <strong>Step 3:</strong> Paste the code to complete login
          </div>
        </div>
        
        <p style="font-size: 14px; opacity: 0.8; margin-top: 30px;">
          ⏰ This code expires in 5 minutes<br>
          🔒 Close this browser window after copying the code
        </p>
      </div>
      
      <script>
        function copyCode() {
          const code = '${loginCode}';
          navigator.clipboard.writeText(code).then(() => {
            const btn = document.querySelector('.copy-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '✅ Copied!';
            btn.style.background = '#4CAF50';
            setTimeout(() => {
              btn.innerHTML = originalText;
              btn.style.background = '#2196F3';
            }, 2000);
          }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = code;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Code copied to clipboard!');
          });
        }
        
        // Auto-select code on page load
        window.onload = function() {
          const codeElement = document.getElementById('loginCode');
          if (window.getSelection && document.createRange) {
            const selection = window.getSelection();
            const range = document.createRange();
            range.selectNodeContents(codeElement);
            selection.removeAllRanges();
            selection.addRange(range);
          }
        };
      </script>
    </body>
    </html>
  `);
});

// New endpoint to exchange login code for token
router.post('/electron/exchange-code', (req, res) => {
  const { code } = req.body;
  
  console.log(`🔄 Code exchange request: ${code}`);
  
  if (!code) {
    return res.status(400).json({ 
      success: false, 
      error: 'Login code is required' 
    });
  }
  
  const tempStore = req.app.get('tempTokenStore') || new Map();
  const stored = tempStore.get(code.toUpperCase());
  
  if (!stored) {
    console.log(`❌ Invalid or expired code: ${code}`);
    return res.status(400).json({ 
      success: false, 
      error: 'Invalid or expired login code' 
    });
  }
  
  // Check if expired
  if (Date.now() > stored.expires) {
    tempStore.delete(code.toUpperCase());
    console.log(`⏰ Expired code: ${code}`);
    return res.status(400).json({ 
      success: false, 
      error: 'Login code has expired' 
    });
  }
  
  // Remove the code (one-time use)
  tempStore.delete(code.toUpperCase());
  
  console.log(`✅ Code exchange successful: ${code}`);
  
  res.json({
    success: true,
    token: stored.token,
    message: 'Login code exchanged successfully'
  });
});

module.exports = router;
